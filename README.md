# Brainstorming App

A Flutter-based brainstorming application that integrates with Claude AI to facilitate creative ideation sessions.

## Features

### Core Features
- 🤖 **AI-Powered Brainstorming**: Real-time conversation with <PERSON>
- 💡 **Smart Follow-ups**: Automatic generation of follow-up questions after each response
- 📝 **Session Management**: Create, archive, and search through brainstorming sessions
- 🏷️ **Tag Organization**: Categorize sessions with custom tags
- 📤 **Export Sessions**: Export conversations as Markdown files
- 🌓 **Dark Mode Support**: Automatic theme switching based on system preferences
- 📱 **Cross-Platform**: Works on iOS, Android, Web, and Desktop

### Navigation & UI
- 🧭 **Intuitive Navigation**: Navigation drawer and bottom navigation bar
- 📊 **Analytics Dashboard**: Session insights and performance metrics (feature-flagged)
- 🔍 **Cross-Session Insights**: Pattern analysis across multiple sessions (feature-flagged)
- 👤 **User Profile**: Personalized settings and preferences
- ⚙️ **Settings**: Feature flags and configuration options
- 🔧 **Claude Setup**: Easy Claude CLI configuration and testing

### Advanced AI Features (Feature-Flagged)
- 🎯 **Session Analysis**: Mood detection, pattern recognition, and key point extraction
- 🧠 **User Modeling**: Personalized profiles and adaptive questioning
- ✅ **Idea Validation**: AI-powered feasibility analysis and improvement suggestions
- 🔗 **Cross-Session Insights**: Pattern analysis across multiple brainstorming sessions
- 🎨 **Vector Embeddings**: Semantic similarity search and topic modeling
- ⚡ **Background Processing**: Efficient AI operations with caching and queuing

### Performance Optimizations
- 🚀 **Claude CLI Keep-Alive**: Reduced startup times for AI interactions
- 🗄️ **Database Optimization**: Performance indexes and optimized queries
- 💾 **Multi-Level Caching**: Memory and disk caching with automatic cleanup
- 📱 **UI Performance**: Optimized list rendering with RepaintBoundary
- 📊 **Performance Monitoring**: Real-time tracking and reporting

## Architecture

The app follows **Clean Architecture** principles with MVVM pattern:

```
lib/
├── domain/           # Business logic layer
│   ├── entities/     # Core business objects
│   │   ├── ai/       # AI-related entities (insights, patterns, etc.)
│   │   └── core/     # Core entities (session, message, tag)
│   ├── repositories/ # Repository interfaces
│   └── use_cases/    # Application business rules
├── data/            # Data layer
│   ├── database/    # SQLite database setup
│   ├── repositories/# Repository implementations
│   └── services/    # External services
│       ├── ai/      # AI analysis services
│       ├── cache/   # Caching services
│       ├── monitoring/ # Performance monitoring
│       └── optimization/ # Performance optimizations
└── presentation/    # UI layer
    ├── screens/     # App screens
    ├── widgets/     # Reusable widgets
    │   ├── navigation/ # Navigation components
    │   └── optimization/ # Performance-optimized widgets
    ├── view_models/ # State management with Riverpod
    ├── router/      # Navigation routing
    └── theme/       # App theming
```

## Prerequisites

1. **Flutter SDK**: Version 3.0.0 or higher
2. **Claude CLI**: Required for AI functionality
   ```bash
   # Install via npm
   npm install -g @anthropic-ai/claude-cli

   # Or via Homebrew (macOS)
   brew install claude

   # Authenticate
   claude auth
   ```

   **Note**: The app includes automatic Claude CLI detection and setup assistance. If Claude CLI is not found, the app will guide you through the installation process.

## Getting Started

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd brainstorming
   ```

2. **Install dependencies**
   ```bash
   flutter pub get
   ```

3. **Run the app**
   ```bash
   # Run on connected device/emulator
   flutter run

   # Run on specific platform
   flutter run -d chrome      # Web
   flutter run -d macos       # macOS
   flutter run -d ios         # iOS
   flutter run -d android     # Android
   ```

## Quick Start Guide

1. **First Launch**: The app will start with a splash screen and initialize performance optimizations
2. **Claude Setup**: If Claude CLI is not detected, navigate to Settings → Claude Setup for installation guidance
3. **Create Session**: Use the "+" button or navigation to create your first brainstorming session
4. **Navigation**:
   - Use the drawer menu (☰) for full navigation options
   - Use bottom navigation for quick access to main features
   - Enable advanced features in Settings → Feature Flags
5. **AI Features**: Advanced AI features (Analytics, Insights) are feature-flagged and can be enabled in Settings

## Development

### Running Tests
```bash
# Run all tests
flutter test

# Run with coverage
flutter test --coverage

# Run specific test file
flutter test test/unit/use_cases/create_session_use_case_test.dart
```

**Current Test Status**:
- **Test Success Rate**: 81.3% (222 passing, 51 failing)
- **Recent Improvements**: Fixed 21+ test files, resolved entity structure mismatches
- **Remaining Issues**: Mostly mock-related issues in integration tests

### Building for Release
```bash
# Android
flutter build apk --release

# iOS
flutter build ios --release

# Web
flutter build web --release

# macOS
flutter build macos --release
```

### Code Generation
The project uses code generation for Riverpod and JSON serialization:
```bash
flutter pub run build_runner build --delete-conflicting-outputs
```

## Key Features Implementation

### Navigation System
- **NavigationMixin**: Reusable navigation functionality across screens
- **Navigation Drawer**: Hierarchical menu with feature-flag integration
  - Core navigation: Home, New Session, Search
  - Analytics & Insights section (feature-flagged)
  - Profile, Settings, and Claude Setup
- **Bottom Navigation**: Quick access to main features
  - Adaptive based on enabled feature flags
  - Context-aware highlighting

### Claude CLI Integration
- **Keep-Alive Optimization**: Maintains warm CLI processes to reduce startup delays
- **Enhanced Error Handling**: Exponential backoff retry (max 2 attempts)
- **Extended Timeout**: 90-second timeout to account for startup time
- **Context Management**: Maintains conversation context (last 10 messages)
- **Performance Monitoring**: Tracks response times and success rates

### Session Management
- **Auto-save**: After each message exchange
- **Archive System**: Older sessions with search capability
- **Full-text Search**: Across sessions, messages, and tags
- **Export Functionality**: Markdown export with formatted conversation history
- **Performance Optimization**: Database indexes for faster queries

### Follow-up System
- **Contextual Questions**: Generates 3-5 relevant follow-up questions
- **Custom Input**: Always includes custom input option
- **History Tracking**: Questions stored with messages for reference

## Database Schema

The app uses SQLite with comprehensive schema including performance optimizations:

### Core Tables
- `sessions`: Brainstorming session metadata
- `messages`: User and AI messages
- `tags`: Categorization tags
- `session_tags`: Many-to-many relationship
- `follow_up_options`: Suggested questions for each AI response

### AI Feature Tables (Feature-Flagged)
- `user_profiles`: User modeling and preferences
- `session_analytics`: Session analysis results
- `cross_session_insights`: Pattern analysis across sessions
- `idea_validations`: AI-powered idea feasibility analysis
- `action_items`: Extracted action items from sessions
- `concept_expansions`: AI-generated concept expansions

### Performance Optimizations
- **12+ Database Indexes**: Optimized for frequently queried columns
- **Query Optimization**: Eliminated N+1 query problems
- **Automatic Cleanup**: Expired data removal and maintenance

## Documentation

### Technical Documentation
- [AI Services Implementation](docs/ai_services_implementation.md) - Advanced AI analysis services
- [AI Repository Implementation](docs/ai_repository_implementation.md) - Data layer implementation for AI features
- [Enhanced Claude CLI Implementation](docs/enhanced_claude_cli_implementation.md) - Extended Claude CLI service
- [AI Repository Interfaces](docs/ai_repository_interfaces.md) - Repository interface definitions
- [Database Migration v2](docs/database_migration_v2.md) - Database schema updates for AI features

### Advanced AI Features
The application includes comprehensive AI-powered features:

- **Session Analysis**: Mood detection, pattern recognition, and key point extraction
- **User Modeling**: Personalized profiles and adaptive questioning
- **Idea Validation**: AI-powered feasibility analysis and improvement suggestions
- **Cross-Session Insights**: Pattern analysis across multiple brainstorming sessions
- **Vector Embeddings**: Semantic similarity search and topic modeling
- **Background Processing**: Efficient AI operations with caching and queuing

All AI features are controlled by feature flags and can be enabled/disabled individually.

## Navigation Structure

The app features a comprehensive navigation system with both drawer and bottom navigation:

### Available Routes
- `/` - Home screen with recent sessions
- `/create-session` - Create new brainstorming session
- `/chat/:sessionId` - Active brainstorming chat
- `/search` - Search across all sessions
- `/analytics/:sessionId` - Session analytics dashboard (feature-flagged)
- `/insights/:userId` - Cross-session insights (feature-flagged)
- `/profile` - User profile and preferences
- `/settings` - Feature flags and app settings
- `/claude-setup` - Claude CLI configuration and testing

### Navigation Components
- **NavigationMixin**: Reusable mixin providing consistent navigation across screens
- **Feature Flag Integration**: Navigation items appear/disappear based on enabled features
- **Context Awareness**: Current route highlighting and appropriate navigation flow
- **Responsive Design**: Adapts to different screen sizes and platforms

## Performance Optimizations

The app includes comprehensive performance optimizations implemented across all layers:

### Claude CLI Performance
- **Keep-Alive Process**: Maintains warm CLI processes to reduce 20-25 second startup delays
- **Process Management**: Automatic cleanup and resource management
- **Timeout Optimization**: Extended timeouts to account for CLI initialization

### Database Performance
- **Performance Indexes**: 12+ indexes on frequently queried columns
- **Query Optimization**: Eliminated N+1 query problems with optimized JOINs
- **Batch Operations**: Efficient bulk operations for large datasets
- **Automatic Cleanup**: Scheduled cleanup of expired data

### UI Performance
- **OptimizedListView**: Custom list components with RepaintBoundary optimization
- **Lazy Loading**: Efficient rendering of large datasets
- **Memory Management**: Automatic cache cleanup and garbage collection
- **Frame Rate Monitoring**: Real-time performance tracking

### Caching Strategy
- **Multi-Level Caching**: Memory and disk caching with TTL management
- **Cache Warming**: Pre-loading frequently accessed data
- **Intelligent Cleanup**: Automatic cache eviction and maintenance
- **Performance Metrics**: Cache hit rates and effectiveness tracking

### Background Processing
- **Isolate Pool**: Heavy computations moved to background isolates
- **Task Scheduling**: Efficient background task management
- **Resource Monitoring**: Memory and CPU usage tracking

## Recent Improvements

### Navigation System (Completed)
- ✅ Implemented NavigationMixin pattern for consistent navigation
- ✅ Added navigation drawer with feature flag integration
- ✅ Created bottom navigation bar with adaptive items
- ✅ Integrated navigation across all major screens
- ✅ Added route highlighting and context awareness

### Test Coverage Improvements (In Progress)
- ✅ **Improved Success Rate**: From 79.4% to 81.3% (222 passing, 51 failing)
- ✅ **Fixed 21+ Test Files**: Resolved entity structure mismatches
- ✅ **Mock Improvements**: Updated repository mocks and service stubs
- 🔄 **Remaining Work**: 51 failing tests (mostly mock-related integration tests)

### Claude CLI Setup (Completed)
- ✅ **Verified Installation**: Claude CLI properly installed and configured
- ✅ **Service Integration**: Updated service to use correct CLI path
- ✅ **Performance Testing**: Confirmed ~27 second response times
- ✅ **Error Handling**: Graceful fallbacks for web environment

### Performance Optimization (Completed)
- ✅ **Claude CLI Keep-Alive**: Implemented process warming for faster subsequent calls
- ✅ **Database Optimization**: Added 12+ performance indexes and optimized queries
- ✅ **UI Performance**: Created optimized list components with RepaintBoundary
- ✅ **Cache Management**: Enhanced existing cache system with cleanup and warming
- ✅ **Performance Monitoring**: Real-time tracking and reporting system

### Current Status
- **App Compilation**: ✅ Successfully builds for web, mobile, and desktop
- **Core Functionality**: ✅ All basic features working correctly
- **Navigation**: ✅ Fully implemented and tested
- **Performance**: ✅ Optimizations implemented and verified
- **AI Features**: ✅ Feature-flagged advanced AI capabilities available
- **Test Coverage**: 🔄 81.3% success rate, ongoing improvements

## Contributing

1. Follow the existing code style and architecture patterns
2. Write tests for new features
3. Update documentation as needed
4. Ensure all tests pass before submitting
5. Consider performance implications of new features
6. Use feature flags for experimental or advanced features

## License

This project is licensed under the MIT License - see the LICENSE file for details.