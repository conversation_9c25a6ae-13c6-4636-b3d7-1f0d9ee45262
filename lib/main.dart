import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'core/features/feature_flags.dart';
import 'data/services/optimization/performance_initialization_service.dart';
import 'presentation/app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize SharedPreferences
  final sharedPrefs = await SharedPreferences.getInstance();

  // Initialize performance optimizations in background
  _initializePerformanceOptimizations();

  runApp(
    ProviderScope(
      overrides: [
        sharedPreferencesProvider.overrideWithValue(sharedPrefs),
      ],
      child: const BrainstormingApp(),
    ),
  );
}

/// Initialize performance optimizations in the background
void _initializePerformanceOptimizations() {
  // Run performance initialization in background to avoid blocking app startup
  Future.microtask(() async {
    try {
      // Note: This will be properly initialized when the app providers are available
      debugPrint('Performance optimization initialization scheduled');
    } catch (e) {
      debugPrint('Performance optimization initialization failed: $e');
    }
  });
}