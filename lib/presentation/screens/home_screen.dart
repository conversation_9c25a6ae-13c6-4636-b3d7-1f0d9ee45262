import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../view_models/session_list_view_model.dart';
import '../widgets/navigation/custom_app_bar.dart';
import '../widgets/navigation/navigation_mixin.dart';
import '../widgets/session_list_tile.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen> with NavigationMixin {
  final _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(sessionListViewModelProvider);
    final viewModel = ref.read(sessionListViewModelProvider.notifier);
    final currentRoute = GoRouterState.of(context).uri.path;

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'Brainstorming Sessions',
        showClaudeStatus: true,
        showSearch: true,
      ),
      drawer: buildNavigationDrawer(context, ref, currentRoute),
      bottomNavigationBar: buildBottomNavigationBar(context, ref, currentRoute),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search sessions...',
                prefixIcon: const Icon(Icons.search),
                suffixIcon: _searchController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          viewModel.searchSessions('');
                        },
                      )
                    : null,
              ),
              onChanged: viewModel.searchSessions,
            ),
          ),
          if (state.error != null)
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.errorContainer,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.error_outline,
                    color: Theme.of(context).colorScheme.onErrorContainer,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      state.error!,
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onErrorContainer,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          Expanded(
            child: state.isLoading
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                    onRefresh: viewModel.loadSessions,
                    child: CustomScrollView(
                      slivers: [
                        if (state.recentSessions.isNotEmpty) ...[
                          const SliverPadding(
                            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            sliver: SliverToBoxAdapter(
                              child: Text(
                                'Recent Sessions',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final session = state.recentSessions[index];
                                return SessionListTile(
                                  session: session,
                                  onTap: () => context.push('/chat/${session.id}'),
                                  onArchive: () => viewModel.archiveSession(session.id),
                                  onDelete: () => _confirmDelete(context, viewModel, session.id),
                                );
                              },
                              childCount: state.recentSessions.length,
                            ),
                          ),
                        ],
                        if (state.archivedSessions.isNotEmpty) ...[
                          const SliverPadding(
                            padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                            sliver: SliverToBoxAdapter(
                              child: Text(
                                'Archived Sessions',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                          SliverList(
                            delegate: SliverChildBuilderDelegate(
                              (context, index) {
                                final session = state.archivedSessions[index];
                                return SessionListTile(
                                  session: session,
                                  onTap: () => context.push('/chat/${session.id}'),
                                  onUnarchive: () => viewModel.unarchiveSession(session.id),
                                  onDelete: () => _confirmDelete(context, viewModel, session.id),
                                );
                              },
                              childCount: state.archivedSessions.length,
                            ),
                          ),
                        ],
                        if (state.recentSessions.isEmpty && state.archivedSessions.isEmpty)
                          SliverFillRemaining(
                            child: Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.lightbulb_outline,
                                    size: 64,
                                    color: Theme.of(context).colorScheme.primary,
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    state.searchQuery.isNotEmpty
                                        ? 'No sessions found'
                                        : 'No brainstorming sessions yet',
                                    style: Theme.of(context).textTheme.headlineSmall,
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    state.searchQuery.isNotEmpty
                                        ? 'Try a different search term'
                                        : 'Start a new session to begin brainstorming',
                                    style: Theme.of(context).textTheme.bodyLarge,
                                  ),
                                ],
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => context.pushNamed('create-session'),
        icon: const Icon(Icons.add),
        label: const Text('New Session'),
      ),
    );
  }

  Future<void> _confirmDelete(BuildContext context, SessionListViewModel viewModel, String sessionId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Session'),
        content: const Text('Are you sure you want to delete this session? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              'Delete',
              style: TextStyle(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      viewModel.deleteSession(sessionId);
    }
  }
}