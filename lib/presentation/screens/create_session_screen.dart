import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../view_models/create_session_view_model.dart';
import '../widgets/navigation/custom_app_bar.dart';
import '../widgets/navigation/navigation_mixin.dart';

class CreateSessionScreen extends ConsumerStatefulWidget {
  const CreateSessionScreen({super.key});

  @override
  ConsumerState<CreateSessionScreen> createState() => _CreateSessionScreenState();
}

class _CreateSessionScreenState extends ConsumerState<CreateSessionScreen> with NavigationMixin {
  final _titleController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void dispose() {
    _titleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createSessionViewModelProvider);
    final viewModel = ref.read(createSessionViewModelProvider.notifier);
    final currentRoute = GoRouterState.of(context).uri.path;

    return Scaffold(
      appBar: const CustomAppBar(
        title: 'New Brainstorming Session',
        showClaudeStatus: true,
        showSearch: false,
      ),
      drawer: buildNavigationDrawer(context, ref, currentRoute),
      body: Form(
        key: _formKey,
        child: ListView(
          padding: const EdgeInsets.all(16),
          children: [
            TextFormField(
              controller: _titleController,
              decoration: const InputDecoration(
                labelText: 'Session Title',
                hintText: 'Enter a title for your brainstorming session',
              ),
              autofocus: true,
              textCapitalization: TextCapitalization.sentences,
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Please enter a title';
                }
                return null;
              },
            ),
            const SizedBox(height: 24),
            Text(
              'Tags',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            const SizedBox(height: 8),
            if (state.popularTags.isNotEmpty) ...[
              Text(
                'Popular tags',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: state.popularTags.map((tag) {
                  final isSelected = state.selectedTags.contains(tag);
                  return FilterChip(
                    label: Text(tag),
                    selected: isSelected,
                    onSelected: (_) => viewModel.toggleTag(tag),
                  );
                }).toList(),
              ),
              const SizedBox(height: 16),
            ],
            TextField(
              decoration: const InputDecoration(
                labelText: 'Add custom tags',
                hintText: 'Type a tag and press enter',
                prefixIcon: Icon(Icons.label_outline),
              ),
              onSubmitted: (value) {
                if (value.trim().isNotEmpty) {
                  viewModel.toggleTag(value.trim());
                  // Clear the field after adding
                }
              },
            ),
            if (state.selectedTags.isNotEmpty) ...[
              const SizedBox(height: 16),
              Text(
                'Selected tags',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(height: 8),
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: state.selectedTags.map((tag) {
                  return Chip(
                    label: Text(tag),
                    onDeleted: () => viewModel.toggleTag(tag),
                  );
                }).toList(),
              ),
            ],
            if (state.error != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: Theme.of(context).colorScheme.onErrorContainer,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        state.error!,
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: ElevatedButton(
            onPressed: state.isCreating
                ? null
                : () async {
                    if (_formKey.currentState!.validate()) {
                      final session = await viewModel.createSession(_titleController.text);
                      if (session != null && context.mounted) {
                        context.go('/chat/${session.id}');
                      }
                    }
                  },
            child: state.isCreating
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Start Brainstorming'),
          ),
        ),
      ),
    );
  }
}