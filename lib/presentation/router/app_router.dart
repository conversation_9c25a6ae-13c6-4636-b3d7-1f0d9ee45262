import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../screens/analytics_dashboard_screen.dart';
import '../screens/chat_screen.dart';
import '../screens/claude_setup_screen.dart';
import '../screens/create_session_screen.dart';
import '../screens/home_screen.dart';
import '../screens/insights_dashboard_screen.dart';
import '../screens/search_screen.dart';
import '../screens/settings/feature_flags_screen.dart';
import '../screens/splash_screen.dart';
import '../screens/user_profile_screen.dart';


final appRouterProvider = Provider<GoRouter>((ref) {
  return GoRouter(
    initialLocation: '/splash',
    routes: [
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      GoRoute(
        path: '/',
        name: 'home',
        builder: (context, state) => const HomeScreen(),
      ),
      GoRoute(
        path: '/create-session',
        name: 'create-session',
        builder: (context, state) => const CreateSessionScreen(),
      ),
      GoRoute(
        path: '/chat/:sessionId',
        name: 'chat',
        builder: (context, state) {
          final sessionId = state.pathParameters['sessionId']!;
          return ChatScreen(sessionId: sessionId);
        },
      ),
      GoRoute(
        path: '/claude-setup',
        name: 'claude-setup',
        builder: (context, state) => const ClaudeSetupScreen(),
      ),
      GoRoute(
        path: '/search',
        name: 'search',
        builder: (context, state) => const SearchScreen(),
      ),
      GoRoute(
        path: '/analytics/:sessionId',
        name: 'analytics',
        builder: (context, state) {
          final sessionId = state.pathParameters['sessionId'] ?? 'overview';
          return AnalyticsDashboardScreen(sessionId: sessionId);
        },
      ),
      GoRoute(
        path: '/insights/:userId',
        name: 'insights',
        builder: (context, state) {
          final userId = state.pathParameters['userId'] ?? 'user';
          return InsightsDashboardScreen(userId: userId);
        },
      ),
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const UserProfileScreen(),
      ),
      GoRoute(
        path: '/settings',
        name: 'settings',
        builder: (context, state) => const FeatureFlagsScreen(),
      ),
    ],
  );
});