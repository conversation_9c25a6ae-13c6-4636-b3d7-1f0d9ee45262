import 'package:flutter/material.dart';

/// Optimized ListView with performance enhancements
class OptimizedListView<T> extends StatelessWidget {
  const OptimizedListView({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.separatorBuilder,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.scrollDirection = Axis.vertical,
    this.reverse = false,
    this.controller,
    this.primary,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.addSemanticIndexes = true,
    this.cacheExtent,
    this.semanticChildCount,
    this.dragStartBehavior = DragStartBehavior.start,
    this.keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    this.restorationId,
    this.clipBehavior = Clip.hardEdge,
  });

  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final Widget Function(BuildContext context, int index)? separatorBuilder;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final Axis scrollDirection;
  final bool reverse;
  final ScrollController? controller;
  final bool? primary;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final bool addSemanticIndexes;
  final double? cacheExtent;
  final int? semanticChildCount;
  final DragStartBehavior dragStartBehavior;
  final ScrollViewKeyboardDismissBehavior keyboardDismissBehavior;
  final String? restorationId;
  final Clip clipBehavior;

  @override
  Widget build(BuildContext context) {
    if (separatorBuilder != null) {
      return ListView.separated(
        key: key,
        itemCount: items.length,
        itemBuilder: (context, index) => _OptimizedListItem<T>(
          key: ValueKey(index),
          item: items[index],
          index: index,
          builder: itemBuilder,
        ),
        separatorBuilder: separatorBuilder!,
        padding: padding,
        shrinkWrap: shrinkWrap,
        physics: physics,
        scrollDirection: scrollDirection,
        reverse: reverse,
        controller: controller,
        primary: primary,
        addAutomaticKeepAlives: addAutomaticKeepAlives,
        addRepaintBoundaries: addRepaintBoundaries,
        addSemanticIndexes: addSemanticIndexes,
        cacheExtent: cacheExtent,
        semanticChildCount: semanticChildCount,
        dragStartBehavior: dragStartBehavior,
        keyboardDismissBehavior: keyboardDismissBehavior,
        restorationId: restorationId,
        clipBehavior: clipBehavior,
      );
    }

    return ListView.builder(
      key: key,
      itemCount: items.length,
      itemBuilder: (context, index) => _OptimizedListItem<T>(
        key: ValueKey(index),
        item: items[index],
        index: index,
        builder: itemBuilder,
      ),
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      scrollDirection: scrollDirection,
      reverse: reverse,
      controller: controller,
      primary: primary,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
      cacheExtent: cacheExtent,
      semanticChildCount: semanticChildCount,
      dragStartBehavior: dragStartBehavior,
      keyboardDismissBehavior: keyboardDismissBehavior,
      restorationId: restorationId,
      clipBehavior: clipBehavior,
    );
  }
}

/// Optimized list item with automatic repaint boundaries
class _OptimizedListItem<T> extends StatelessWidget {
  const _OptimizedListItem({
    super.key,
    required this.item,
    required this.index,
    required this.builder,
  });

  final T item;
  final int index;
  final Widget Function(BuildContext context, T item, int index) builder;

  @override
  Widget build(BuildContext context) {
    return RepaintBoundary(
      child: builder(context, item, index),
    );
  }
}

/// Optimized grid view with performance enhancements
class OptimizedGridView<T> extends StatelessWidget {
  const OptimizedGridView({
    super.key,
    required this.items,
    required this.itemBuilder,
    required this.gridDelegate,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.scrollDirection = Axis.vertical,
    this.reverse = false,
    this.controller,
    this.primary,
    this.addAutomaticKeepAlives = true,
    this.addRepaintBoundaries = true,
    this.addSemanticIndexes = true,
    this.cacheExtent,
    this.semanticChildCount,
    this.dragStartBehavior = DragStartBehavior.start,
    this.keyboardDismissBehavior = ScrollViewKeyboardDismissBehavior.manual,
    this.restorationId,
    this.clipBehavior = Clip.hardEdge,
  });

  final List<T> items;
  final Widget Function(BuildContext context, T item, int index) itemBuilder;
  final SliverGridDelegate gridDelegate;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final Axis scrollDirection;
  final bool reverse;
  final ScrollController? controller;
  final bool? primary;
  final bool addAutomaticKeepAlives;
  final bool addRepaintBoundaries;
  final bool addSemanticIndexes;
  final double? cacheExtent;
  final int? semanticChildCount;
  final DragStartBehavior dragStartBehavior;
  final ScrollViewKeyboardDismissBehavior keyboardDismissBehavior;
  final String? restorationId;
  final Clip clipBehavior;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      key: key,
      itemCount: items.length,
      itemBuilder: (context, index) => _OptimizedListItem<T>(
        key: ValueKey(index),
        item: items[index],
        index: index,
        builder: itemBuilder,
      ),
      gridDelegate: gridDelegate,
      padding: padding,
      shrinkWrap: shrinkWrap,
      physics: physics,
      scrollDirection: scrollDirection,
      reverse: reverse,
      controller: controller,
      primary: primary,
      addAutomaticKeepAlives: addAutomaticKeepAlives,
      addRepaintBoundaries: addRepaintBoundaries,
      addSemanticIndexes: addSemanticIndexes,
      cacheExtent: cacheExtent,
      semanticChildCount: semanticChildCount,
      dragStartBehavior: dragStartBehavior,
      keyboardDismissBehavior: keyboardDismissBehavior,
      restorationId: restorationId,
      clipBehavior: clipBehavior,
    );
  }
}

/// Optimized animated list with performance enhancements
class OptimizedAnimatedList<T> extends StatefulWidget {
  const OptimizedAnimatedList({
    super.key,
    required this.items,
    required this.itemBuilder,
    this.removedItemBuilder,
    this.padding,
    this.shrinkWrap = false,
    this.physics,
    this.scrollDirection = Axis.vertical,
    this.reverse = false,
    this.controller,
    this.primary,
    this.clipBehavior = Clip.hardEdge,
    this.initialItemCount,
  });

  final List<T> items;
  final Widget Function(BuildContext context, T item, int index, Animation<double> animation) itemBuilder;
  final Widget Function(BuildContext context, T item, int index, Animation<double> animation)? removedItemBuilder;
  final EdgeInsetsGeometry? padding;
  final bool shrinkWrap;
  final ScrollPhysics? physics;
  final Axis scrollDirection;
  final bool reverse;
  final ScrollController? controller;
  final bool? primary;
  final Clip clipBehavior;
  final int? initialItemCount;

  @override
  State<OptimizedAnimatedList<T>> createState() => _OptimizedAnimatedListState<T>();
}

class _OptimizedAnimatedListState<T> extends State<OptimizedAnimatedList<T>> {
  final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();
  late List<T> _items;

  @override
  void initState() {
    super.initState();
    _items = List.from(widget.items);
  }

  @override
  void didUpdateWidget(OptimizedAnimatedList<T> oldWidget) {
    super.didUpdateWidget(oldWidget);
    _updateItems(oldWidget.items, widget.items);
  }

  void _updateItems(List<T> oldItems, List<T> newItems) {
    // Simple implementation - in production, you'd want more sophisticated diffing
    if (newItems.length > oldItems.length) {
      // Items added
      for (int i = oldItems.length; i < newItems.length; i++) {
        _items.add(newItems[i]);
        _listKey.currentState?.insertItem(i);
      }
    } else if (newItems.length < oldItems.length) {
      // Items removed
      for (int i = oldItems.length - 1; i >= newItems.length; i--) {
        final removedItem = _items.removeAt(i);
        _listKey.currentState?.removeItem(
          i,
          (context, animation) => widget.removedItemBuilder?.call(context, removedItem, i, animation) ??
              widget.itemBuilder(context, removedItem, i, animation),
        );
      }
    }
    _items = List.from(newItems);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedList(
      key: _listKey,
      initialItemCount: widget.initialItemCount ?? _items.length,
      itemBuilder: (context, index, animation) {
        if (index >= _items.length) return const SizedBox.shrink();
        
        return RepaintBoundary(
          child: widget.itemBuilder(context, _items[index], index, animation),
        );
      },
      padding: widget.padding,
      shrinkWrap: widget.shrinkWrap,
      physics: widget.physics,
      scrollDirection: widget.scrollDirection,
      reverse: widget.reverse,
      controller: widget.controller,
      primary: widget.primary,
      clipBehavior: widget.clipBehavior,
    );
  }
}
