import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/features/feature_flags.dart';

/// Mixin that provides navigation functionality to screens
mixin NavigationMixin {
  /// Builds a navigation drawer for the screen
  Widget buildNavigationDrawer(BuildContext context, WidgetRef ref, String currentRoute) {
    final featureFlags = ref.watch(featureFlagServiceProvider);
    
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.lightbulb,
                  size: 48,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                const SizedBox(height: 8),
                Text(
                  'Brainstorming',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'AI-Powered Ideation',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.home,
            title: 'Home',
            route: '/',
            isSelected: currentRoute == '/',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.add_circle,
            title: 'New Session',
            route: '/create-session',
            isSelected: currentRoute == '/create-session',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.search,
            title: 'Search Sessions',
            route: '/search',
            isSelected: currentRoute == '/search',
          ),
          const Divider(),
          if (featureFlags.isEnabled(FeatureFlag.sessionSummaries) ||
              featureFlags.isEnabled(FeatureFlag.crossSessionInsights))
            _buildDrawerSection(
              context,
              title: 'Analytics & Insights',
              children: [
                if (featureFlags.isEnabled(FeatureFlag.sessionSummaries))
                  _buildDrawerItem(
                    context,
                    icon: Icons.analytics,
                    title: 'Analytics',
                    route: '/analytics/overview',
                    isSelected: currentRoute.startsWith('/analytics'),
                    isSubItem: true,
                  ),
                if (featureFlags.isEnabled(FeatureFlag.crossSessionInsights))
                  _buildDrawerItem(
                    context,
                    icon: Icons.insights,
                    title: 'Insights',
                    route: '/insights/user',
                    isSelected: currentRoute.startsWith('/insights'),
                    isSubItem: true,
                  ),
              ],
            ),
          const Divider(),
          _buildDrawerItem(
            context,
            icon: Icons.person,
            title: 'Profile',
            route: '/profile',
            isSelected: currentRoute == '/profile',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.settings,
            title: 'Settings',
            route: '/settings',
            isSelected: currentRoute == '/settings',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.build,
            title: 'Claude Setup',
            route: '/claude-setup',
            isSelected: currentRoute == '/claude-setup',
          ),
        ],
      ),
    );
  }

  /// Builds a bottom navigation bar for the screen
  Widget? buildBottomNavigationBar(BuildContext context, WidgetRef ref, String currentRoute) {
    final featureFlags = ref.watch(featureFlagServiceProvider);
    final currentIndex = _getCurrentBottomNavIndex(currentRoute, featureFlags);
    
    // Only show bottom nav on main screens
    if (!_shouldShowBottomNav(currentRoute)) {
      return null;
    }
    
    final items = <BottomNavigationBarItem>[
      const BottomNavigationBarItem(
        icon: Icon(Icons.home),
        label: 'Home',
      ),
      const BottomNavigationBarItem(
        icon: Icon(Icons.add_circle),
        label: 'New',
      ),
    ];

    // Add feature-flagged items
    if (featureFlags.isEnabled(FeatureFlag.sessionSummaries)) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.analytics),
        label: 'Analytics',
      ));
    }
    
    if (featureFlags.isEnabled(FeatureFlag.crossSessionInsights)) {
      items.add(const BottomNavigationBarItem(
        icon: Icon(Icons.insights),
        label: 'Insights',
      ));
    }

    items.add(const BottomNavigationBarItem(
      icon: Icon(Icons.person),
      label: 'Profile',
    ));
    
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex.clamp(0, items.length - 1),
      onTap: (index) => _onBottomNavTap(context, index, featureFlags),
      items: items,
    );
  }

  Widget _buildDrawerSection(BuildContext context, {required String title, required List<Widget> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ...children,
      ],
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String route,
    required bool isSelected,
    bool isSubItem = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected 
          ? Theme.of(context).colorScheme.primary 
          : Theme.of(context).colorScheme.onSurface,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected 
            ? Theme.of(context).colorScheme.primary 
            : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      contentPadding: EdgeInsets.only(
        left: isSubItem ? 32 : 16,
        right: 16,
      ),
      onTap: () {
        Navigator.of(context).pop(); // Close drawer
        context.go(route);
      },
    );
  }

  bool _shouldShowBottomNav(String currentRoute) {
    // Don't show bottom nav on chat screens or splash
    return !currentRoute.startsWith('/chat') && 
           currentRoute != '/splash';
  }

  int _getCurrentBottomNavIndex(String currentRoute, FeatureFlagService featureFlags) {
    if (currentRoute == '/') return 0;
    if (currentRoute == '/create-session') return 1;
    
    int index = 2;
    if (featureFlags.isEnabled(FeatureFlag.sessionSummaries)) {
      if (currentRoute.startsWith('/analytics')) return index;
      index++;
    }
    
    if (featureFlags.isEnabled(FeatureFlag.crossSessionInsights)) {
      if (currentRoute.startsWith('/insights')) return index;
      index++;
    }
    
    if (currentRoute == '/profile') return index;
    
    return 0; // Default to home
  }

  void _onBottomNavTap(BuildContext context, int index, FeatureFlagService featureFlags) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/create-session');
        break;
      case 2:
        if (featureFlags.isEnabled(FeatureFlag.sessionSummaries)) {
          context.go('/analytics/overview');
        } else if (featureFlags.isEnabled(FeatureFlag.crossSessionInsights)) {
          context.go('/insights/user');
        } else {
          context.go('/profile');
        }
        break;
      case 3:
        if (featureFlags.isEnabled(FeatureFlag.sessionSummaries) && 
            featureFlags.isEnabled(FeatureFlag.crossSessionInsights)) {
          context.go('/insights/user');
        } else {
          context.go('/profile');
        }
        break;
      case 4:
        context.go('/profile');
        break;
    }
  }
}
