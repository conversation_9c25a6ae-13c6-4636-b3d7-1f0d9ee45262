import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../core/features/feature_flags.dart';

/// Main navigation wrapper that provides drawer and bottom navigation
class MainNavigation extends ConsumerWidget {
  const MainNavigation({
    required this.child,
    required this.currentRoute,
    super.key,
  });

  final Widget child;
  final String currentRoute;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final featureFlags = ref.watch(featureFlagServiceProvider);
    
    return Scaffold(
      drawer: _buildNavigationDrawer(context, ref, featureFlags),
      body: child,
      bottomNavigationBar: _buildBottomNavigation(context, featureFlags),
    );
  }

  Widget _buildNavigationDrawer(BuildContext context, WidgetRef ref, FeatureFlagService featureFlags) {
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.lightbulb,
                  size: 48,
                  color: Theme.of(context).colorScheme.onPrimary,
                ),
                const SizedBox(height: 8),
                Text(
                  'Brainstorming',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'AI-Powered Ideation',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(context).colorScheme.onPrimary.withValues(alpha: 0.8),
                  ),
                ),
              ],
            ),
          ),
          _buildDrawerItem(
            context,
            icon: Icons.home,
            title: 'Home',
            route: '/',
            isSelected: currentRoute == '/',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.add_circle,
            title: 'New Session',
            route: '/create-session',
            isSelected: currentRoute == '/create-session',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.search,
            title: 'Search Sessions',
            route: '/search',
            isSelected: currentRoute == '/search',
          ),
          const Divider(),
          if (featureFlags.isEnabled(FeatureFlag.sessionSummaries) ||
              featureFlags.isEnabled(FeatureFlag.crossSessionInsights))
            _buildDrawerSection(
              context,
              title: 'Analytics & Insights',
              children: [
                if (featureFlags.isEnabled(FeatureFlag.sessionSummaries))
                  _buildDrawerItem(
                    context,
                    icon: Icons.analytics,
                    title: 'Analytics',
                    route: '/analytics/overview',
                    isSelected: currentRoute.startsWith('/analytics'),
                    isSubItem: true,
                  ),
                if (featureFlags.isEnabled(FeatureFlag.crossSessionInsights))
                  _buildDrawerItem(
                    context,
                    icon: Icons.insights,
                    title: 'Insights',
                    route: '/insights/user',
                    isSelected: currentRoute.startsWith('/insights'),
                    isSubItem: true,
                  ),
              ],
            ),
          const Divider(),
          _buildDrawerItem(
            context,
            icon: Icons.person,
            title: 'Profile',
            route: '/profile',
            isSelected: currentRoute == '/profile',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.settings,
            title: 'Settings',
            route: '/settings',
            isSelected: currentRoute == '/settings',
          ),
          _buildDrawerItem(
            context,
            icon: Icons.build,
            title: 'Claude Setup',
            route: '/claude-setup',
            isSelected: currentRoute == '/claude-setup',
          ),
        ],
      ),
    );
  }

  Widget _buildDrawerSection(BuildContext context, {required String title, required List<Widget> children}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.labelLarge?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        ...children,
      ],
    );
  }

  Widget _buildDrawerItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required String route,
    required bool isSelected,
    bool isSubItem = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isSelected 
          ? Theme.of(context).colorScheme.primary 
          : Theme.of(context).colorScheme.onSurface,
      ),
      title: Text(
        title,
        style: TextStyle(
          color: isSelected 
            ? Theme.of(context).colorScheme.primary 
            : Theme.of(context).colorScheme.onSurface,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      selected: isSelected,
      contentPadding: EdgeInsets.only(
        left: isSubItem ? 32 : 16,
        right: 16,
      ),
      onTap: () {
        Navigator.of(context).pop(); // Close drawer
        context.go(route);
      },
    );
  }

  Widget _buildBottomNavigation(BuildContext context, FeatureFlagService featureFlags) {
    final currentIndex = _getCurrentBottomNavIndex();
    
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      currentIndex: currentIndex,
      onTap: (index) => _onBottomNavTap(context, index),
      items: [
        const BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'Home',
        ),
        const BottomNavigationBarItem(
          icon: Icon(Icons.add_circle),
          label: 'New',
        ),
        if (featureFlags.isEnabled(FeatureFlag.sessionSummaries))
          const BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Analytics',
          ),
        if (featureFlags.isEnabled(FeatureFlag.crossSessionInsights))
          const BottomNavigationBarItem(
            icon: Icon(Icons.insights),
            label: 'Insights',
          ),
        const BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'Profile',
        ),
      ],
    );
  }

  int _getCurrentBottomNavIndex() {
    if (currentRoute == '/') return 0;
    if (currentRoute == '/create-session') return 1;
    if (currentRoute.startsWith('/analytics')) return 2;
    if (currentRoute.startsWith('/insights')) return 3;
    if (currentRoute == '/profile') return 4;
    return 0; // Default to home
  }

  void _onBottomNavTap(BuildContext context, int index) {
    switch (index) {
      case 0:
        context.go('/');
        break;
      case 1:
        context.go('/create-session');
        break;
      case 2:
        context.go('/analytics/overview');
        break;
      case 3:
        context.go('/insights/user');
        break;
      case 4:
        context.go('/profile');
        break;
    }
  }
}
