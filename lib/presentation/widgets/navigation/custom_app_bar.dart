import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../view_models/chat_view_model.dart';

/// Custom app bar that provides consistent navigation and actions
class CustomAppBar extends ConsumerWidget implements PreferredSizeWidget {
  const CustomAppBar({
    required this.title,
    this.actions,
    this.showClaudeStatus = true,
    this.showSearch = true,
    super.key,
  });

  final String title;
  final List<Widget>? actions;
  final bool showClaudeStatus;
  final bool showSearch;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final claudeAvailable = ref.watch(chatViewModelProvider).claudeAvailable;

    return AppBar(
      title: Text(title),
      actions: [
        // Claude status indicator
        if (showClaudeStatus && !claudeAvailable)
          IconButton(
            icon: const Icon(Icons.warning, color: Colors.orange),
            onPressed: () => context.pushNamed('claude-setup'),
            tooltip: 'Claude CLI not available - Click to setup',
          ),
        
        // Search button
        if (showSearch)
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => context.pushNamed('search'),
            tooltip: 'Smart Search',
          ),
        
        // Custom actions
        if (actions != null) ...actions!,
        
        // Overflow menu
        PopupMenuButton<String>(
          onSelected: (value) => _handleMenuAction(context, value),
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'refresh',
              child: ListTile(
                leading: Icon(Icons.refresh),
                title: Text('Refresh'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'settings',
              child: ListTile(
                leading: Icon(Icons.settings),
                title: Text('Settings'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
            const PopupMenuItem(
              value: 'help',
              child: ListTile(
                leading: Icon(Icons.help),
                title: Text('Help'),
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _handleMenuAction(BuildContext context, String action) {
    switch (action) {
      case 'refresh':
        // Trigger refresh - this could be handled by the parent screen
        break;
      case 'settings':
        context.go('/settings');
        break;
      case 'help':
        _showHelpDialog(context);
        break;
    }
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Welcome to the Brainstorming App!'),
            SizedBox(height: 16),
            Text('• Use the drawer to navigate between sections'),
            Text('• Create new sessions with the + button'),
            Text('• Search through your sessions with the search icon'),
            Text('• Access analytics and insights from the navigation'),
            Text('• Configure features in Settings'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }
}

/// Simplified app bar for screens that don't need all features
class SimpleAppBar extends StatelessWidget implements PreferredSizeWidget {
  const SimpleAppBar({
    required this.title,
    this.actions,
    super.key,
  });

  final String title;
  final List<Widget>? actions;

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: Text(title),
      actions: actions,
    );
  }
}
