import 'dart:async';

import '../../../domain/entities/message.dart';
import '../../../domain/entities/session.dart';
import '../../repositories/user_modeling_repository_impl.dart';

/// Service for modeling user behavior and preferences
class UserModelingService {
  const UserModelingService({
    required UserModelingRepositoryImpl userProfileRepository,
  }) : _userProfileRepository = userProfileRepository;

  // ignore: unused_field
  final UserModelingRepositoryImpl _userProfileRepository;

  /// Initializes a new user profile
  Future<UserProfile> initializeProfile(String userId) async {
    final profile = UserProfile(
      userId: userId,
      preferences: const UserPreferences(
        questionComplexity: 'balanced',
        responseLength: 'medium',
        questionTypes: [],
        focusAreas: [],
      ),
      topicInterests: const {},
      sessionHistory: SessionHistory(
        totalSessions: 0,
        averageSessionLength: Duration.zero,
        preferredTimeOfDay: 'morning',
        lastActiveAt: DateTime.now(),
      ),
      thinkingPatterns: const [],
      lastUpdated: DateTime.now(),
    );

    return profile;
  }

  /// Updates user profile from session data
  Future<UserProfile> updateFromSession(Session session) async {
    // Create a mock existing profile for now
    final existingProfile = UserProfile(
      userId: session.userId ?? 'unknown',
      preferences: const UserPreferences(
        questionComplexity: 'balanced',
        responseLength: 'medium',
        questionTypes: ['analytical'],
        focusAreas: [],
      ),
      topicInterests: const {},
      sessionHistory: SessionHistory(
        totalSessions: 5,
        averageSessionLength: const Duration(minutes: 20),
        preferredTimeOfDay: 'morning',
        lastActiveAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      thinkingPatterns: const [],
      lastUpdated: DateTime.now().subtract(const Duration(days: 1)),
    );
    
    final userMessages = session.messages.where((m) => !m.isAi).toList();
    final sessionDuration = session.updatedAt.difference(session.createdAt);
    
    // Update session history
    final newTotalSessions = existingProfile.sessionHistory.totalSessions + 1;
    final newAverageLength = Duration(
      milliseconds: (existingProfile.sessionHistory.averageSessionLength.inMilliseconds * 
          existingProfile.sessionHistory.totalSessions + sessionDuration.inMilliseconds) ~/ 
          newTotalSessions
    );
    
    final timeOfDay = _getTimeOfDay(session.createdAt);
    
    final updatedSessionHistory = SessionHistory(
      totalSessions: newTotalSessions,
      averageSessionLength: newAverageLength,
      preferredTimeOfDay: timeOfDay,
      lastActiveAt: session.updatedAt,
    );
    
    // Extract topics from session
    final newTopicInterests = Map<String, double>.from(existingProfile.topicInterests);
    await _extractTopicsFromSession(session, newTopicInterests);
    
    // Update thinking patterns
    final newThinkingPatterns = List<ThinkingPattern>.from(existingProfile.thinkingPatterns);
    await _updateThinkingPatterns(userMessages, newThinkingPatterns);
    
    final updatedProfile = existingProfile.copyWith(
      sessionHistory: updatedSessionHistory,
      topicInterests: newTopicInterests,
      thinkingPatterns: newThinkingPatterns,
      lastUpdated: DateTime.now(),
    );
    
    return updatedProfile;
  }

  /// Gets personalized suggestions for the user
  Future<List<String>> getPersonalizedSuggestions(
    String userId,
    String currentContext,
  ) async {
    // Mock profile for now
    final profile = UserProfile(
      userId: userId,
      preferences: const UserPreferences(
        questionComplexity: 'detailed',
        responseLength: 'comprehensive',
        questionTypes: ['analytical', 'creative'],
        focusAreas: ['technology', 'innovation'],
      ),
      topicInterests: const {
        'artificial intelligence': 0.8,
        'machine learning': 0.7,
        'robotics': 0.5,
      },
      sessionHistory: SessionHistory(
        totalSessions: 10,
        averageSessionLength: const Duration(minutes: 25),
        preferredTimeOfDay: 'evening',
        lastActiveAt: DateTime.now(),
      ),
      thinkingPatterns: const [
        ThinkingPattern(
          type: 'analytical',
          strength: 0.8,
          examples: ['breaking down complex problems', 'systematic analysis'],
        ),
      ],
      lastUpdated: DateTime.now(),
    );
    
    final suggestions = <String>[];
    
    // Generate suggestions based on user interests and patterns
    final topInterests = profile.topicInterests.entries
        .toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    // Add interest-based suggestions
    for (final interest in topInterests.take(3)) {
      if (_contextMatches(currentContext, interest.key)) {
        suggestions.add('Let\'s explore ${interest.key} in more depth');
        suggestions.add('What aspects of ${interest.key} interest you most?');
      }
    }
    
    // Add complexity-based suggestions
    switch (profile.preferences.questionComplexity) {
      case 'detailed':
        suggestions.add('Can you break down the technical details of this approach?');
        suggestions.add('What are the long-term implications we should consider?');
        break;
      case 'simple':
        suggestions.add('What\'s the main takeaway here?');
        suggestions.add('How would you summarize this in simple terms?');
        break;
      default:
        suggestions.add('What do you think about this?');
        suggestions.add('How might we approach this differently?');
    }
    
    // Add pattern-based suggestions
    for (final pattern in profile.thinkingPatterns) {
      switch (pattern.type) {
        case 'analytical':
          suggestions.add('What data or evidence supports this idea?');
          break;
        case 'creative':
          suggestions.add('What if we tried a completely different approach?');
          break;
        case 'collaborative':
          suggestions.add('How might others contribute to this idea?');
          break;
      }
    }
    
    // Ensure we don't exceed 5 suggestions and always include custom input
    final uniqueSuggestions = suggestions.take(4).toList();
    uniqueSuggestions.add('Custom input');
    
    return uniqueSuggestions;
  }

  /// Predicts user intent from a message
  Future<UserIntent> predictUserIntent(String userId, String message) async {
    // Mock profile for now
    final profile = UserProfile(
      userId: userId,
      preferences: const UserPreferences(
        questionComplexity: 'detailed',
        responseLength: 'comprehensive',
        questionTypes: ['exploratory', 'analytical'],
        focusAreas: ['technology'],
      ),
      topicInterests: const {
        'web development': 0.9,
        'javascript': 0.8,
      },
      sessionHistory: SessionHistory(
        totalSessions: 20,
        averageSessionLength: const Duration(minutes: 30),
        preferredTimeOfDay: 'morning',
        lastActiveAt: DateTime.now(),
      ),
      thinkingPatterns: const [],
      lastUpdated: DateTime.now(),
    );
    
    final messageLower = message.toLowerCase();
    
    // Analyze intent based on message patterns
    String primaryIntent;
    final List<String> secondaryIntents = [];
    double confidence = 0.5;
    String suggestedApproach = '';
    List<String> relatedTopics = [];
    
    // Simple greeting detection
    if (_isGreeting(messageLower)) {
      primaryIntent = 'greeting';
      confidence = 0.8;
      suggestedApproach = 'Respond with a warm greeting and ask about their goals';
    }
    // Problem-solving intent
    else if (_isProblemSolving(messageLower)) {
      primaryIntent = 'problem_solving';
      confidence = 0.8;
      suggestedApproach = 'Break down the problem and explore solutions systematically';
      relatedTopics = _extractRelatedTopics(messageLower, profile);
    }
    // Information seeking
    else if (_isInformationSeeking(messageLower)) {
      primaryIntent = 'information_seeking';
      confidence = 0.7;
      suggestedApproach = 'Provide comprehensive information and examples';
    }
    // Creative exploration
    else if (_isCreativeExploration(messageLower)) {
      primaryIntent = 'creative_exploration';
      confidence = 0.7;
      suggestedApproach = 'Encourage brainstorming and innovative thinking';
    }
    // Default to exploration
    else {
      primaryIntent = 'exploration';
      confidence = 0.5;
      suggestedApproach = 'Ask clarifying questions to understand the goal';
    }
    
    return UserIntent(
      primary: primaryIntent,
      secondary: secondaryIntents,
      confidence: confidence,
      suggestedApproach: suggestedApproach,
      relatedTopics: relatedTopics,
    );
  }

  /// Infers user preferences from messages
  Future<UserPreferences> inferPreferences(List<Message> messages) async {
    final userMessages = messages.where((m) => !m.isAi).toList();
    
    if (userMessages.isEmpty) {
      return const UserPreferences(
        questionComplexity: 'balanced',
        responseLength: 'medium',
        questionTypes: [],
        focusAreas: [],
      );
    }
    
    // Analyze message complexity
    final avgLength = userMessages.map((m) => m.content.length).reduce((a, b) => a + b) / userMessages.length;
    String complexity;
    String responseLength;
    
    if (avgLength > 200) {
      complexity = 'detailed';
      responseLength = 'comprehensive';
    } else if (avgLength < 50) {
      complexity = 'simple';
      responseLength = 'concise';
    } else {
      complexity = 'balanced';
      responseLength = 'medium';
    }
    
    // Detect question types based on content
    final questionTypes = <String>[];
    final allContent = userMessages.map((m) => m.content.toLowerCase()).join(' ');
    
    if (allContent.contains('analyze') || allContent.contains('break down') || allContent.contains('detailed')) {
      questionTypes.add('analytical');
    }
    if (allContent.contains('creative') || allContent.contains('innovative') || allContent.contains('brainstorm')) {
      questionTypes.add('creative');
    }
    if (allContent.contains('what if') || allContent.contains('explore') || allContent.contains('possibilities')) {
      questionTypes.add('exploratory');
    }
    if (allContent.contains('implement') || allContent.contains('how to') || allContent.contains('practical')) {
      questionTypes.add('practical');
    }
    
    return UserPreferences(
      questionComplexity: complexity,
      responseLength: responseLength,
      questionTypes: questionTypes,
      focusAreas: [], // Can be inferred from topic analysis
    );
  }

  // Helper methods
  String _getTimeOfDay(DateTime dateTime) {
    final hour = dateTime.hour;
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'night';
  }

  Future<void> _extractTopicsFromSession(Session session, Map<String, double> topicInterests) async {
    final content = '${session.title} ${session.messages.map((m) => m.content).join(' ')}'.toLowerCase();
    
    // Simple keyword extraction
    final technicalKeywords = ['ai', 'artificial intelligence', 'machine learning', 'technology', 'programming', 'software', 'development'];
    final designKeywords = ['design', 'user experience', 'ui', 'ux', 'interface', 'visual'];
    final businessKeywords = ['business', 'strategy', 'marketing', 'growth', 'revenue'];
    
    for (final keyword in technicalKeywords) {
      if (content.contains(keyword)) {
        topicInterests[keyword] = (topicInterests[keyword] ?? 0.0) + 0.1;
      }
    }
    
    for (final keyword in designKeywords) {
      if (content.contains(keyword)) {
        topicInterests[keyword] = (topicInterests[keyword] ?? 0.0) + 0.1;
      }
    }
    
    for (final keyword in businessKeywords) {
      if (content.contains(keyword)) {
        topicInterests[keyword] = (topicInterests[keyword] ?? 0.0) + 0.1;
      }
    }
  }

  Future<void> _updateThinkingPatterns(List<Message> userMessages, List<ThinkingPattern> patterns) async {
    final content = userMessages.map((m) => m.content.toLowerCase()).join(' ');
    
    // Update or add analytical pattern
    if (content.contains('analyze') || content.contains('systematic') || content.contains('data')) {
      final existingIndex = patterns.indexWhere((p) => p.type == 'analytical');
      if (existingIndex >= 0) {
        patterns[existingIndex] = patterns[existingIndex].copyWith(
          strength: (patterns[existingIndex].strength + 0.1).clamp(0.0, 1.0),
        );
      } else {
        patterns.add(const ThinkingPattern(
          type: 'analytical',
          strength: 0.7,
          examples: ['Systematic analysis', 'Data-driven approach'],
        ));
      }
    }
    
    // Update or add creative pattern
    if (content.contains('creative') || content.contains('innovative') || content.contains('brainstorm')) {
      final existingIndex = patterns.indexWhere((p) => p.type == 'creative');
      if (existingIndex >= 0) {
        patterns[existingIndex] = patterns[existingIndex].copyWith(
          strength: (patterns[existingIndex].strength + 0.1).clamp(0.0, 1.0),
        );
      } else {
        patterns.add(const ThinkingPattern(
          type: 'creative',
          strength: 0.7,
          examples: ['Creative thinking', 'Innovative solutions'],
        ));
      }
    }
  }

  bool _contextMatches(String context, String topic) {
    final contextLower = context.toLowerCase();
    final topicLower = topic.toLowerCase();

    // Direct match
    if (contextLower.contains(topicLower)) {
      return true;
    }

    // Handle common abbreviations
    if (topicLower == 'artificial intelligence' && contextLower.contains('ai')) {
      return true;
    }
    if (topicLower == 'machine learning' && contextLower.contains('ml')) {
      return true;
    }

    return false;
  }

  bool _isGreeting(String message) {
    final greetings = ['hello', 'hi', 'hey', 'good morning', 'good afternoon'];
    return greetings.any((greeting) => message.startsWith(greeting));
  }

  bool _isProblemSolving(String message) {
    final keywords = ['how can i', 'solve', 'fix', 'optimize', 'improve', 'problem', 'issue'];
    return keywords.any((keyword) => message.contains(keyword));
  }

  bool _isInformationSeeking(String message) {
    final keywords = ['what is', 'tell me about', 'explain', 'how does', 'information'];
    return keywords.any((keyword) => message.contains(keyword));
  }

  bool _isCreativeExploration(String message) {
    final keywords = ['brainstorm', 'creative', 'innovative', 'ideas', 'imagine'];
    return keywords.any((keyword) => message.contains(keyword));
  }

  List<String> _extractRelatedTopics(String message, UserProfile profile) {
    final relatedTopics = <String>[];
    for (final topic in profile.topicInterests.keys) {
      if (message.contains(topic.toLowerCase())) {
        relatedTopics.add(topic);
      }
    }
    if (message.toLowerCase().contains('optimization') || message.toLowerCase().contains('optimize')) {
      relatedTopics.add('optimization');
    }
    return relatedTopics.take(3).toList();
  }
}

/// User profile for personalization (expected by tests)
class UserProfile {
  const UserProfile({
    required this.userId,
    required this.preferences,
    required this.topicInterests,
    required this.sessionHistory,
    required this.thinkingPatterns,
    required this.lastUpdated,
  });

  final String userId;
  final UserPreferences preferences;
  final Map<String, double> topicInterests;
  final SessionHistory sessionHistory;
  final List<ThinkingPattern> thinkingPatterns;
  final DateTime lastUpdated;

  UserProfile copyWith({
    String? userId,
    UserPreferences? preferences,
    Map<String, double>? topicInterests,
    SessionHistory? sessionHistory,
    List<ThinkingPattern>? thinkingPatterns,
    DateTime? lastUpdated,
  }) {
    return UserProfile(
      userId: userId ?? this.userId,
      preferences: preferences ?? this.preferences,
      topicInterests: topicInterests ?? this.topicInterests,
      sessionHistory: sessionHistory ?? this.sessionHistory,
      thinkingPatterns: thinkingPatterns ?? this.thinkingPatterns,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }
}

/// User preferences for personalization (expected by tests)
class UserPreferences {
  const UserPreferences({
    required this.questionComplexity,
    required this.responseLength,
    required this.questionTypes,
    required this.focusAreas,
  });

  final String questionComplexity; // 'simple', 'balanced', 'detailed'
  final String responseLength; // 'concise', 'medium', 'comprehensive'
  final List<String> questionTypes; // ['analytical', 'creative', etc.]
  final List<String> focusAreas; // ['technology', 'design', etc.]

  UserPreferences copyWith({
    String? questionComplexity,
    String? responseLength,
    List<String>? questionTypes,
    List<String>? focusAreas,
  }) {
    return UserPreferences(
      questionComplexity: questionComplexity ?? this.questionComplexity,
      responseLength: responseLength ?? this.responseLength,
      questionTypes: questionTypes ?? this.questionTypes,
      focusAreas: focusAreas ?? this.focusAreas,
    );
  }
}

/// Session history for user modeling (expected by tests)
class SessionHistory {
  const SessionHistory({
    required this.totalSessions,
    required this.averageSessionLength,
    required this.preferredTimeOfDay,
    required this.lastActiveAt,
  });

  final int totalSessions;
  final Duration averageSessionLength;
  final String preferredTimeOfDay;
  final DateTime lastActiveAt;

  SessionHistory copyWith({
    int? totalSessions,
    Duration? averageSessionLength,
    String? preferredTimeOfDay,
    DateTime? lastActiveAt,
  }) {
    return SessionHistory(
      totalSessions: totalSessions ?? this.totalSessions,
      averageSessionLength: averageSessionLength ?? this.averageSessionLength,
      preferredTimeOfDay: preferredTimeOfDay ?? this.preferredTimeOfDay,
      lastActiveAt: lastActiveAt ?? this.lastActiveAt,
    );
  }
}

/// Thinking pattern identified from user behavior (expected by tests)
class ThinkingPattern {
  const ThinkingPattern({
    required this.type,
    required this.strength,
    required this.examples,
  });

  final String type;
  final double strength;
  final List<String> examples;

  ThinkingPattern copyWith({
    String? type,
    double? strength,
    List<String>? examples,
  }) {
    return ThinkingPattern(
      type: type ?? this.type,
      strength: strength ?? this.strength,
      examples: examples ?? this.examples,
    );
  }
}

/// User intent prediction result
class UserIntent {
  const UserIntent({
    required this.primary,
    required this.secondary,
    required this.confidence,
    required this.suggestedApproach,
    required this.relatedTopics,
  });

  final String primary;
  final List<String> secondary;
  final double confidence;
  final String suggestedApproach;
  final List<String> relatedTopics;
}