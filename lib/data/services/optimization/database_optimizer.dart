import 'dart:async';
import 'package:sqflite/sqflite.dart';
import '../../database/database_helper.dart';

/// Service for optimizing database performance
class DatabaseOptimizer {
  final DatabaseHelper _databaseHelper;
  
  DatabaseOptimizer(this._databaseHelper);

  /// Create performance indexes for frequently queried columns
  Future<void> createPerformanceIndexes() async {
    final db = await _databaseHelper.database;
    
    await db.transaction((txn) async {
      // Sessions table indexes
      await _createIndexIfNotExists(txn, 'idx_sessions_updated_at', 
          'CREATE INDEX IF NOT EXISTS idx_sessions_updated_at ON ${DatabaseHelper.sessionsTable} (updated_at DESC)');
      
      await _createIndexIfNotExists(txn, 'idx_sessions_archived', 
          'CREATE INDEX IF NOT EXISTS idx_sessions_archived ON ${DatabaseHelper.sessionsTable} (is_archived)');
      
      await _createIndexIfNotExists(txn, 'idx_sessions_user_id', 
          'CREATE INDEX IF NOT EXISTS idx_sessions_user_id ON ${DatabaseHelper.sessionsTable} (user_id)');
      
      // Messages table indexes
      await _createIndexIfNotExists(txn, 'idx_messages_session_id', 
          'CREATE INDEX IF NOT EXISTS idx_messages_session_id ON ${DatabaseHelper.messagesTable} (session_id)');
      
      await _createIndexIfNotExists(txn, 'idx_messages_timestamp', 
          'CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON ${DatabaseHelper.messagesTable} (timestamp DESC)');
      
      // Session tags table indexes
      await _createIndexIfNotExists(txn, 'idx_session_tags_session_id', 
          'CREATE INDEX IF NOT EXISTS idx_session_tags_session_id ON ${DatabaseHelper.sessionTagsTable} (session_id)');
      
      await _createIndexIfNotExists(txn, 'idx_session_tags_tag_id', 
          'CREATE INDEX IF NOT EXISTS idx_session_tags_tag_id ON ${DatabaseHelper.sessionTagsTable} (tag_id)');
      
      // Cross-session insights indexes
      await _createIndexIfNotExists(txn, 'idx_insights_type', 
          'CREATE INDEX IF NOT EXISTS idx_insights_type ON ${DatabaseHelper.crossSessionInsightsTable} (insight_type)');
      
      await _createIndexIfNotExists(txn, 'idx_insights_created_at', 
          'CREATE INDEX IF NOT EXISTS idx_insights_created_at ON ${DatabaseHelper.crossSessionInsightsTable} (created_at DESC)');
      
      // Concept expansions indexes
      await _createIndexIfNotExists(txn, 'idx_concepts_original', 
          'CREATE INDEX IF NOT EXISTS idx_concepts_original ON ${DatabaseHelper.conceptExpansionsTable} (original_concept)');
      
      await _createIndexIfNotExists(txn, 'idx_concepts_expires_at', 
          'CREATE INDEX IF NOT EXISTS idx_concepts_expires_at ON ${DatabaseHelper.conceptExpansionsTable} (expires_at)');
      
      // Idea validations indexes
      await _createIndexIfNotExists(txn, 'idx_validations_score', 
          'CREATE INDEX IF NOT EXISTS idx_validations_score ON ${DatabaseHelper.ideaValidationsTable} (feasibility_score DESC)');
      
      // Tags usage index for popular tags query
      await _createIndexIfNotExists(txn, 'idx_tags_name', 
          'CREATE INDEX IF NOT EXISTS idx_tags_name ON ${DatabaseHelper.tagsTable} (name)');
    });
  }

  /// Helper method to create index if it doesn't exist
  Future<void> _createIndexIfNotExists(Transaction txn, String indexName, String createSql) async {
    try {
      await txn.execute(createSql);
    } catch (e) {
      // Index might already exist, which is fine
      print('Index $indexName creation skipped (might already exist): $e');
    }
  }

  /// Optimize a query to fetch sessions with their tags in a single operation
  Future<List<Map<String, dynamic>>> getSessionsWithTagsOptimized({
    String? userId,
    bool includeArchived = false,
    int? limit,
    String? orderBy,
  }) async {
    final db = await _databaseHelper.database;

    var query = '''
      SELECT
        s.id,
        s.title,
        s.summary,
        s.created_at,
        s.updated_at,
        s.is_archived,
        GROUP_CONCAT(t.name) as tag_names
      FROM ${DatabaseHelper.sessionsTable} s
      LEFT JOIN ${DatabaseHelper.sessionTagsTable} st ON s.id = st.session_id
      LEFT JOIN ${DatabaseHelper.tagsTable} t ON st.tag_id = t.id
    ''';

    final whereConditions = <String>[];
    final whereArgs = <dynamic>[];

    // Note: user_id column doesn't exist in current schema
    // TODO: Add user_id column in future migration if user support is needed
    if (userId != null) {
      // For now, ignore userId parameter since column doesn't exist
      // This prevents database errors while maintaining API compatibility
    }
    
    if (!includeArchived) {
      whereConditions.add('s.is_archived = 0');
    }
    
    if (whereConditions.isNotEmpty) {
      query += ' WHERE ${whereConditions.join(' AND ')}';
    }
    
    query += ' GROUP BY s.id';
    
    if (orderBy != null) {
      query += ' ORDER BY $orderBy';
    } else {
      query += ' ORDER BY s.updated_at DESC';
    }
    
    if (limit != null) {
      query += ' LIMIT $limit';
    }
    
    return await db.rawQuery(query, whereArgs);
  }

  /// Optimize message loading with pagination
  Future<List<Map<String, dynamic>>> getMessagesOptimized(
    String sessionId, {
    int? limit,
    int? offset,
    String? orderBy,
  }) async {
    final db = await _databaseHelper.database;
    
    var query = '''
      SELECT id, session_id, content, role, timestamp
      FROM ${DatabaseHelper.messagesTable}
      WHERE session_id = ?
    ''';
    
    final args = [sessionId];
    
    if (orderBy != null) {
      query += ' ORDER BY $orderBy';
    } else {
      query += ' ORDER BY timestamp ASC';
    }
    
    if (limit != null) {
      query += ' LIMIT $limit';
      if (offset != null) {
        query += ' OFFSET $offset';
      }
    }
    
    return await db.rawQuery(query, args);
  }

  /// Batch insert optimization for multiple messages
  Future<void> batchInsertMessages(List<Map<String, dynamic>> messages) async {
    final db = await _databaseHelper.database;
    
    await db.transaction((txn) async {
      final batch = txn.batch();
      
      for (final message in messages) {
        batch.insert(DatabaseHelper.messagesTable, message);
      }
      
      await batch.commit(noResult: true);
    });
  }

  /// Analyze database performance and suggest optimizations
  Future<Map<String, dynamic>> analyzePerformance() async {
    final db = await _databaseHelper.database;
    
    // Get table sizes
    final tableSizes = <String, int>{};
    final tables = [
      DatabaseHelper.sessionsTable,
      DatabaseHelper.messagesTable,
      DatabaseHelper.tagsTable,
      DatabaseHelper.sessionTagsTable,
      DatabaseHelper.crossSessionInsightsTable,
      DatabaseHelper.conceptExpansionsTable,
      DatabaseHelper.ideaValidationsTable,
    ];
    
    for (final table in tables) {
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM $table');
      tableSizes[table] = result.first['count'] as int;
    }
    
    // Check for missing indexes (simplified check)
    final indexInfo = await db.rawQuery("SELECT name FROM sqlite_master WHERE type='index'");
    final existingIndexes = indexInfo.map((row) => row['name'] as String).toList();
    
    return {
      'table_sizes': tableSizes,
      'existing_indexes': existingIndexes,
      'recommendations': _generateRecommendations(tableSizes, existingIndexes),
    };
  }

  List<String> _generateRecommendations(Map<String, int> tableSizes, List<String> existingIndexes) {
    final recommendations = <String>[];
    
    // Check for large tables without proper indexes
    if (tableSizes[DatabaseHelper.messagesTable]! > 1000 && 
        !existingIndexes.contains('idx_messages_session_id')) {
      recommendations.add('Add index on messages.session_id for better query performance');
    }
    
    if (tableSizes[DatabaseHelper.sessionsTable]! > 100 && 
        !existingIndexes.contains('idx_sessions_updated_at')) {
      recommendations.add('Add index on sessions.updated_at for better sorting performance');
    }
    
    // Check for cleanup opportunities
    final totalInsights = tableSizes[DatabaseHelper.crossSessionInsightsTable]!;
    if (totalInsights > 500) {
      recommendations.add('Consider implementing cleanup for old insights (current: $totalInsights)');
    }
    
    return recommendations;
  }

  /// Clean up expired data to improve performance
  Future<int> cleanupExpiredData() async {
    final db = await _databaseHelper.database;
    int totalDeleted = 0;
    
    await db.transaction((txn) async {
      // Clean up expired concept expansions
      final expiredConcepts = await txn.delete(
        DatabaseHelper.conceptExpansionsTable,
        where: 'expires_at IS NOT NULL AND expires_at < ?',
        whereArgs: [DateTime.now().millisecondsSinceEpoch],
      );
      totalDeleted += expiredConcepts;
      
      // Clean up old insights (older than 90 days)
      final cutoff = DateTime.now().subtract(const Duration(days: 90)).millisecondsSinceEpoch;
      final oldInsights = await txn.delete(
        DatabaseHelper.crossSessionInsightsTable,
        where: 'created_at < ?',
        whereArgs: [cutoff],
      );
      totalDeleted += oldInsights;
    });
    
    // Run VACUUM to reclaim space
    await db.execute('VACUUM');
    
    return totalDeleted;
  }
}
