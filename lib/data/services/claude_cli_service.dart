import 'dart:async';
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import '../../domain/entities/message.dart';
import '../../domain/failures/failures.dart';

class ClaudeCliService {
  static const int maxRetries = 2; // Reduced retries since each attempt is slow
  static const Duration timeout = Duration(seconds: 90); // 1.5 minutes to account for startup time
  static const Duration retryDelay = Duration(seconds: 1);
  static const Duration keepAliveInterval = Duration(minutes: 5); // Keep CLI warm

  String _claudePath = '/opt/homebrew/bin/claude'; // Updated to correct path

  // Performance optimization: Keep CLI process warm
  Process? _keepAliveProcess;
  Timer? _keepAliveTimer;
  DateTime? _lastUsed;

  // Getter for subclasses
  @protected
  String get claudePath => _claudePath;

  Future<Either<Failure, bool>> checkCliAvailability() async {
    try {
      // Try different possible locations for Claude CLI
      final claudePaths = [
        '/opt/homebrew/bin/claude', // Most likely location first
        'claude',
        '/usr/local/bin/claude',
        '/Users/<USER>/.bun/bin/claude',
      ];
      
      for (final claudePath in claudePaths) {
        try {
          final result = await Process.run(claudePath, ['--version']);
          debugPrint('Checking Claude at: $claudePath');
          debugPrint('Exit code: ${result.exitCode}');
          debugPrint('Stdout: ${result.stdout}');
          debugPrint('Stderr: ${result.stderr}');
          
          if (result.exitCode == 0) {
            _claudePath = claudePath;
            debugPrint('Claude CLI found at: $_claudePath');
            return const Right(true);
          }
        } catch (e) {
          debugPrint('Error checking $claudePath: $e');
          // Try next path
        }
      }
      
      debugPrint('Claude CLI not found in any location');
      return const Right(false);
    } catch (e) {
      debugPrint('General error in checkCliAvailability: $e');
      return const Right(false);
    }
  }

  Future<Either<Failure, Stream<String>>> sendMessage(
    String message,
    List<Message> conversationHistory,
  ) async {
    try {
      // Build the conversation context
      final context = _buildConversationContext(conversationHistory);
      final fullPrompt = context.isNotEmpty 
        ? '$context\n\nUser: $message'
        : message;
      
      debugPrint('Sending message to Claude CLI...');
      debugPrint('Prompt length: ${fullPrompt.length} characters');
      debugPrint('Note: Using optimized CLI with keep-alive for better performance');

      // Performance optimization: Start keep-alive if not running
      await _ensureKeepAlive();

      // Create a stream controller for the response
      final controller = StreamController<String>();

      // Run in a separate isolate to avoid blocking
      Future(() async {
        try {
          for (int attempt = 0; attempt < maxRetries; attempt++) {
            try {
              // Try with environment variables to disable interactive mode
              final result = await Process.run(
                _claudePath,
                ['--print', fullPrompt],
                environment: {
                  'TERM': 'dumb',
                  'FORCE_COLOR': '0',
                  'CI': 'true',
                  'NO_COLOR': '1',
                  'NODE_NO_READLINE': '1',
                },
              ).timeout(timeout);
              
              if (result.exitCode == 0) {
                final response = result.stdout.toString();
                debugPrint('Claude response received: ${response.length} characters');
                
                // Stream the response in chunks to simulate streaming
                // First chunk immediately to show response started
                if (response.isNotEmpty) {
                  final firstChunk = response.substring(0, response.length > 50 ? 50 : response.length);
                  controller.add(firstChunk);
                  
                  // Stream the rest
                  const chunkSize = 30;
                  for (int i = firstChunk.length; i < response.length; i += chunkSize) {
                    final end = (i + chunkSize < response.length) ? i + chunkSize : response.length;
                    final chunk = response.substring(i, end);
                    controller.add(chunk);
                    await Future.delayed(const Duration(milliseconds: 3)); // Very fast streaming
                  }
                }
                controller.close();
                return;
              } else {
                debugPrint('Claude CLI error: ${result.stderr}');
                if (attempt == maxRetries - 1) {
                  controller.addError(ClaudeCliFailure('CLI Error: ${result.stderr}'));
                  controller.close();
                }
              }
            } catch (e) {
              debugPrint('Error calling Claude (attempt ${attempt + 1}): $e');
              if (attempt == maxRetries - 1) {
                if (e.toString().contains('TimeoutException')) {
                  controller.addError(ClaudeCliFailure.timeout());
                } else {
                  controller.addError(ClaudeCliFailure(e.toString()));
                }
                controller.close();
              } else {
                await Future.delayed(retryDelay * (attempt + 1));
              }
            }
          }
        } catch (e) {
          controller.addError(ClaudeCliFailure(e.toString()));
          controller.close();
        }
      });

      return Right(controller.stream);
    } catch (e) {
      return Left(ClaudeCliFailure(e.toString()));
    }
  }

  Future<Either<Failure, List<String>>> generateFollowUpQuestions(
    String lastResponse,
    List<Message> conversationHistory,
  ) async {
    try {
      final context = _buildConversationContext(conversationHistory);
      final prompt = '''
$context

Based on the conversation above and my last response, generate 3-5 follow-up questions that the user might want to ask next. 
Format each question on a new line, starting with "- ".
Keep questions concise and relevant to the brainstorming session.

Last response: $lastResponse
''';

      final result = await Process.run(
        _claudePath,
        ['--print', prompt],
        environment: {
          'TERM': 'dumb',
          'FORCE_COLOR': '0',
          'CI': 'true',
          'NO_COLOR': '1',
          'NODE_NO_READLINE': '1',
        },
      ).timeout(timeout);
      
      if (result.exitCode != 0) {
        return Left(ClaudeCliFailure('CLI Error: ${result.stderr}'));
      }
      
      final response = result.stdout.toString();
      
      // Parse the questions from the response
      final questions = response
          .split('\n')
          .where((line) => line.trim().startsWith('- '))
          .map((line) => line.substring(2).trim())
          .where((q) => q.isNotEmpty)
          .take(5)
          .toList();

      if (questions.isEmpty) {
        // Fallback questions if parsing fails
        return const Right([
          'Can you elaborate on this idea?',
          'What are the potential challenges?',
          'How would this work in practice?',
        ]);
      }

      return Right(questions);
    } catch (e) {
      return Left(ClaudeCliFailure(e.toString()));
    }
  }

  Future<Either<Failure, String>> generateSessionSummary(
    List<Message> messages,
  ) async {
    try {
      final context = _buildConversationContext(messages);
      final prompt = '''
$context

Generate a concise summary (2-3 sentences) of this brainstorming session.
Focus on the main topics discussed and key insights or decisions made.
''';

      final result = await Process.run(
        _claudePath,
        ['--print', prompt],
        environment: {
          'TERM': 'dumb',
          'FORCE_COLOR': '0',
          'CI': 'true',
          'NO_COLOR': '1',
          'NODE_NO_READLINE': '1',
        },
      ).timeout(timeout);
      
      if (result.exitCode != 0) {
        return Left(ClaudeCliFailure('CLI Error: ${result.stderr}'));
      }
      
      final summary = result.stdout.toString().trim();
      return Right(summary);
    } catch (e) {
      return Left(ClaudeCliFailure(e.toString()));
    }
  }


  String _buildConversationContext(List<Message> messages) {
    if (messages.isEmpty) return '';

    return messages
        .map((msg) => '${msg.role == MessageRole.user ? 'User' : 'Assistant'}: ${msg.content}')
        .join('\n\n');
  }

  /// Performance optimization: Keep Claude CLI process warm to avoid startup delays
  Future<void> _ensureKeepAlive() async {
    _lastUsed = DateTime.now();

    // Start keep-alive timer if not already running
    _keepAliveTimer ??= Timer.periodic(keepAliveInterval, (_) => _performKeepAlive());

    // If no keep-alive process is running, start one
    if (_keepAliveProcess == null) {
      await _startKeepAlive();
    }
  }

  /// Start a keep-alive process to keep CLI warm
  Future<void> _startKeepAlive() async {
    try {
      debugPrint('Starting Claude CLI keep-alive process...');

      // Start a simple version check to warm up the CLI
      _keepAliveProcess = await Process.start(
        _claudePath,
        ['--version'],
        environment: {
          'TERM': 'dumb',
          'FORCE_COLOR': '0',
          'CI': 'true',
          'NO_COLOR': '1',
          'NODE_NO_READLINE': '1',
        },
      );

      // Listen for process completion
      _keepAliveProcess!.exitCode.then((_) {
        _keepAliveProcess = null;
      });

      debugPrint('Claude CLI keep-alive process started');
    } catch (e) {
      debugPrint('Failed to start keep-alive process: $e');
      _keepAliveProcess = null;
    }
  }

  /// Perform periodic keep-alive to maintain CLI warmth
  Future<void> _performKeepAlive() async {
    final now = DateTime.now();

    // If CLI hasn't been used recently, stop keep-alive to save resources
    if (_lastUsed != null && now.difference(_lastUsed!) > keepAliveInterval * 2) {
      await _stopKeepAlive();
      return;
    }

    // Restart keep-alive process if it died
    if (_keepAliveProcess == null) {
      await _startKeepAlive();
    }
  }

  /// Stop keep-alive process and timer
  Future<void> _stopKeepAlive() async {
    debugPrint('Stopping Claude CLI keep-alive...');

    _keepAliveTimer?.cancel();
    _keepAliveTimer = null;

    _keepAliveProcess?.kill();
    _keepAliveProcess = null;
  }

  /// Dispose resources
  void dispose() {
    _stopKeepAlive();
  }
}