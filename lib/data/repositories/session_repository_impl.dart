import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:intl/intl.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';

import '../../domain/entities/message.dart';
import '../../domain/entities/session.dart';
import '../../domain/failures/failures.dart';
import '../../domain/repositories/session_repository.dart';
import '../database/database_helper.dart';
import '../services/optimization/database_optimizer.dart';

class SessionRepositoryImpl implements SessionRepository {

  SessionRepositoryImpl(this._databaseHelper);
  final DatabaseHelper _databaseHelper;
  final _uuid = const Uuid();

  // Performance optimization: Lazy-initialized database optimizer
  DatabaseOptimizer? _dbOptimizer;
  DatabaseOptimizer get _optimizer => _dbOptimizer ??= DatabaseOptimizer(_databaseHelper);

  @override
  Future<Either<Failure, Session>> createSession(String title, List<String> tags) async {
    try {
      final db = await _databaseHelper.database;
      final sessionId = _uuid.v4();
      final now = DateTime.now();

      await db.transaction((txn) async {
        // Insert session
        await txn.insert(
          DatabaseHelper.sessionsTable,
          {
            'id': sessionId,
            'title': title,
            'created_at': now.millisecondsSinceEpoch,
            'updated_at': now.millisecondsSinceEpoch,
            'is_archived': 0,
          },
        );

        // Insert tags if any
        for (final tagName in tags) {
          // Check if tag exists
          final existingTags = await txn.query(
            DatabaseHelper.tagsTable,
            where: 'name = ?',
            whereArgs: [tagName],
          );

          String tagId;
          if (existingTags.isEmpty) {
            // Create new tag
            tagId = _uuid.v4();
            await txn.insert(
              DatabaseHelper.tagsTable,
              {
                'id': tagId,
                'name': tagName,
                'color': _generateTagColor(),
                'created_at': now.millisecondsSinceEpoch,
              },
            );
          } else {
            tagId = existingTags.first['id'] as String;
          }

          // Link tag to session
          await txn.insert(
            DatabaseHelper.sessionTagsTable,
            {
              'session_id': sessionId,
              'tag_id': tagId,
            },
          );
        }
      });

      final session = Session(
        id: sessionId,
        title: title,
        createdAt: now,
        updatedAt: now,
        tags: tags.map((tag) => tag.toString()).toList().cast<String>(),
        messages: const [], // New session starts with empty messages
        userId: null, // Can be set later
      );

      return Right(session);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Session?>> getSession(String sessionId) async {
    try {
      final db = await _databaseHelper.database;
      
      // Get session
      final sessions = await db.query(
        DatabaseHelper.sessionsTable,
        where: 'id = ?',
        whereArgs: [sessionId],
      );

      if (sessions.isEmpty) {
        return const Right(null);
      }

      // Get tags
      final tags = await db.rawQuery('''
        SELECT t.name 
        FROM ${DatabaseHelper.tagsTable} t
        JOIN ${DatabaseHelper.sessionTagsTable} st ON t.id = st.tag_id
        WHERE st.session_id = ?
      ''', [sessionId]);

      final sessionData = sessions.first;
      final session = Session(
        id: sessionData['id'] as String,
        title: sessionData['title'] as String,
        createdAt: DateTime.fromMillisecondsSinceEpoch(sessionData['created_at'] as int),
        updatedAt: DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at'] as int),
        summary: sessionData['summary'] as String?,
        isArchived: (sessionData['is_archived'] as int) == 1,
        tags: tags.map((t) => (t['name'] ?? '').toString()).toList().cast<String>(),
        messages: const [], // Default empty list, can be loaded separately via getSessionMessages
        userId: sessionData['user_id'] as String?,
      );

      return Right(session);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Session?> getSessionById(String sessionId) async {
    try {
      final result = await getSession(sessionId);
      return result.fold(
        (failure) => null,
        (session) => session,
      );
    } catch (e) {
      return null;
    }
  }

  @override
  Future<Either<Failure, List<Session>>> getRecentSessions({int limit = 10}) async {
    try {
      // Performance optimization: Use optimized query to avoid N+1 problem
      final sessionsWithTags = await _optimizer.getSessionsWithTagsOptimized(
        includeArchived: false,
        limit: limit,
        orderBy: 's.updated_at DESC',
      );

      final sessionList = sessionsWithTags.map((sessionData) {
        final tagNames = sessionData['tag_names'] as String?;
        final tags = tagNames?.split(',').where((tag) => tag.isNotEmpty).toList() ?? <String>[];

        return Session(
          id: sessionData['id'] as String,
          title: sessionData['title'] as String,
          createdAt: DateTime.fromMillisecondsSinceEpoch(sessionData['created_at'] as int),
          updatedAt: DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at'] as int),
          summary: sessionData['summary'] as String?,
          isArchived: (sessionData['is_archived'] as int) == 1,
          tags: tags,
          messages: const [], // Default empty list, can be loaded separately via getSessionMessages
          userId: sessionData['user_id'] as String?,
        );
      }).toList();

      return Right(sessionList);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Session>>> getArchivedSessions() async {
    try {
      final db = await _databaseHelper.database;
      
      final sessions = await db.query(
        DatabaseHelper.sessionsTable,
        where: 'is_archived = 1',
        orderBy: 'updated_at DESC',
      );

      final sessionList = <Session>[];
      for (final sessionData in sessions) {
        final sessionId = sessionData['id'] as String;
        
        final tags = await db.rawQuery('''
          SELECT t.name 
          FROM ${DatabaseHelper.tagsTable} t
          JOIN ${DatabaseHelper.sessionTagsTable} st ON t.id = st.tag_id
          WHERE st.session_id = ?
        ''', [sessionId]);

        sessionList.add(Session(
          id: sessionId,
          title: sessionData['title'] as String,
          createdAt: DateTime.fromMillisecondsSinceEpoch(sessionData['created_at'] as int),
          updatedAt: DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at'] as int),
          summary: sessionData['summary'] as String?,
          isArchived: true,
          tags: tags.map((t) => (t['name'] ?? '').toString()).toList().cast<String>(),
          messages: const [], // Default empty list, can be loaded separately via getSessionMessages
          userId: sessionData['user_id'] as String?,
        ));
      }

      return Right(sessionList);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Session>>> getUserSessions(String userId, {int? limit}) async {
    try {
      final db = await _databaseHelper.database;

      // Note: user_id column doesn't exist in current schema
      // For now, return all non-archived sessions
      // TODO: Add user_id column in future migration if user support is needed
      final sessions = await db.query(
        DatabaseHelper.sessionsTable,
        where: 'is_archived = 0',
        orderBy: 'updated_at DESC',
        limit: limit,
      );

      final sessionList = <Session>[];
      for (final sessionData in sessions) {
        final sessionId = sessionData['id'] as String;
        
        final tags = await db.rawQuery('''
          SELECT t.name 
          FROM ${DatabaseHelper.tagsTable} t
          JOIN ${DatabaseHelper.sessionTagsTable} st ON t.id = st.tag_id
          WHERE st.session_id = ?
        ''', [sessionId]);

        sessionList.add(Session(
          id: sessionId,
          title: sessionData['title'] as String,
          createdAt: DateTime.fromMillisecondsSinceEpoch(sessionData['created_at'] as int),
          updatedAt: DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at'] as int),
          summary: sessionData['summary'] as String?,
          isArchived: false,
          tags: tags.map((t) => (t['name'] ?? '').toString()).toList().cast<String>(),
          messages: const [], // Default empty list, can be loaded separately via getSessionMessages
          userId: sessionData['user_id'] as String?,
        ));
      }

      return Right(sessionList);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Session>>> searchSessions(String query) async {
    try {
      final db = await _databaseHelper.database;
      final searchQuery = '%${query.toLowerCase()}%';
      
      final sessions = await db.rawQuery('''
        SELECT DISTINCT s.* 
        FROM ${DatabaseHelper.sessionsTable} s
        LEFT JOIN ${DatabaseHelper.messagesTable} m ON s.id = m.session_id
        LEFT JOIN ${DatabaseHelper.sessionTagsTable} st ON s.id = st.session_id
        LEFT JOIN ${DatabaseHelper.tagsTable} t ON st.tag_id = t.id
        WHERE LOWER(s.title) LIKE ? 
           OR LOWER(s.summary) LIKE ?
           OR LOWER(m.content) LIKE ?
           OR LOWER(t.name) LIKE ?
        ORDER BY s.updated_at DESC
      ''', [searchQuery, searchQuery, searchQuery, searchQuery]);

      final sessionList = <Session>[];
      for (final sessionData in sessions) {
        final sessionId = sessionData['id'] as String;
        
        final tags = await db.rawQuery('''
          SELECT t.name 
          FROM ${DatabaseHelper.tagsTable} t
          JOIN ${DatabaseHelper.sessionTagsTable} st ON t.id = st.tag_id
          WHERE st.session_id = ?
        ''', [sessionId]);

        sessionList.add(Session(
          id: sessionId,
          title: sessionData['title'] as String,
          createdAt: DateTime.fromMillisecondsSinceEpoch(sessionData['created_at'] as int),
          updatedAt: DateTime.fromMillisecondsSinceEpoch(sessionData['updated_at'] as int),
          summary: sessionData['summary'] as String?,
          isArchived: (sessionData['is_archived'] as int) == 1,
          tags: tags.map((t) => (t['name'] ?? '').toString()).toList().cast<String>(),
          messages: const [], // Default empty list, can be loaded separately via getSessionMessages
          userId: sessionData['user_id'] as String?,
        ));
      }

      return Right(sessionList);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Session>> updateSession(Session session) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.update(
        DatabaseHelper.sessionsTable,
        {
          'title': session.title,
          'summary': session.summary,
          'updated_at': session.updatedAt.millisecondsSinceEpoch,
          'is_archived': session.isArchived ? 1 : 0,
        },
        where: 'id = ?',
        whereArgs: [session.id],
      );

      return Right(session);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> deleteSession(String sessionId) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.delete(
        DatabaseHelper.sessionsTable,
        where: 'id = ?',
        whereArgs: [sessionId],
      );

      return const Right(null);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> archiveSession(String sessionId) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.update(
        DatabaseHelper.sessionsTable,
        {
          'is_archived': 1,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [sessionId],
      );

      return const Right(null);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> unarchiveSession(String sessionId) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.update(
        DatabaseHelper.sessionsTable,
        {
          'is_archived': 0,
          'updated_at': DateTime.now().millisecondsSinceEpoch,
        },
        where: 'id = ?',
        whereArgs: [sessionId],
      );

      return const Right(null);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, Message>> addMessage(Message message) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.transaction((txn) async {
        // Insert message
        await txn.insert(
          DatabaseHelper.messagesTable,
          {
            'id': message.id,
            'session_id': message.sessionId,
            'content': message.content,
            'role': message.role.name,
            'timestamp': message.timestamp.millisecondsSinceEpoch,
          },
        );

        // Update session timestamp
        await txn.update(
          DatabaseHelper.sessionsTable,
          {'updated_at': message.timestamp.millisecondsSinceEpoch},
          where: 'id = ?',
          whereArgs: [message.sessionId],
        );
      });

      return Right(message);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, List<Message>>> getSessionMessages(String sessionId) async {
    try {
      final db = await _databaseHelper.database;
      
      final messages = await db.query(
        DatabaseHelper.messagesTable,
        where: 'session_id = ?',
        whereArgs: [sessionId],
        orderBy: 'timestamp DESC',
      );

      final messageList = <Message>[];
      for (final messageData in messages) {
        final messageId = messageData['id'] as String;
        
        // Get follow-up options if any
        final options = await db.query(
          DatabaseHelper.followUpOptionsTable,
          where: 'message_id = ?',
          whereArgs: [messageId],
          orderBy: 'order_index',
        );

        messageList.add(Message(
          id: messageId,
          sessionId: messageData['session_id'] as String,
          content: messageData['content'] as String,
          role: MessageRole.values.firstWhere(
            (r) => r.name == messageData['role'],
          ),
          timestamp: DateTime.fromMillisecondsSinceEpoch(messageData['timestamp'] as int),
          followUpOptions: options.isNotEmpty
              ? options.map((o) => o['option_text'] as String).toList()
              : null,
        ));
      }

      return Right(messageList.reversed.toList());
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> updateFollowUpOptions(String messageId, List<String> options) async {
    try {
      final db = await _databaseHelper.database;
      
      await db.transaction((txn) async {
        // Delete existing options
        await txn.delete(
          DatabaseHelper.followUpOptionsTable,
          where: 'message_id = ?',
          whereArgs: [messageId],
        );

        // Insert new options
        for (int i = 0; i < options.length; i++) {
          await txn.insert(
            DatabaseHelper.followUpOptionsTable,
            {
              'message_id': messageId,
              'option_text': options[i],
              'order_index': i,
            },
          );
        }
      });

      return const Right(null);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, String>> exportSessionAsMarkdown(String sessionId) async {
    try {
      final sessionResult = await getSession(sessionId);
      return sessionResult.fold(
        (failure) => Left(failure),
        (session) async {
          if (session == null) {
            return Left(StorageFailure.notFound());
          }

          final messagesResult = await getSessionMessages(sessionId);
          return messagesResult.fold(
            (failure) => Left(failure),
            (messages) {
              final dateFormat = DateFormat('yyyy-MM-dd HH:mm:ss');
              final buffer = StringBuffer();

              // Header
              buffer.writeln('# ${session.title}');
              buffer.writeln('Date: ${dateFormat.format(session.createdAt)}');
              if (session.tags.isNotEmpty) {
                buffer.writeln('Tags: ${session.tags.join(', ')}');
              }
              
              final duration = session.updatedAt.difference(session.createdAt);
              buffer.writeln('Duration: ${_formatDuration(duration)}');
              buffer.writeln();

              // Summary
              if (session.summary != null) {
                buffer.writeln('## Summary');
                buffer.writeln(session.summary);
                buffer.writeln();
              }

              // Conversation
              buffer.writeln('## Conversation');
              for (final message in messages) {
                final role = message.role == MessageRole.user ? 'User' : 'AI';
                buffer.writeln('**$role**: ${message.content}');
                
                if (message.followUpOptions != null && message.followUpOptions!.isNotEmpty) {
                  buffer.writeln('\n*Follow-up options:*');
                  for (final option in message.followUpOptions!) {
                    buffer.writeln('- $option');
                  }
                }
                buffer.writeln();
              }

              return Right(buffer.toString());
            },
          );
        },
      );
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  @override
  Future<Either<Failure, void>> saveExportedFile(String sessionId, String content, String format) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final exportDir = Directory(path.join(directory.path, 'brainstorming_exports'));
      if (!await exportDir.exists()) {
        await exportDir.create(recursive: true);
      }

      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final fileName = 'session_${sessionId}_$timestamp.$format';
      final file = File(path.join(exportDir.path, fileName));
      
      await file.writeAsString(content);
      
      return const Right(null);
    } catch (e) {
      return Left(StorageFailure.databaseError(e.toString()));
    }
  }

  String _generateTagColor() {
    final colors = [
      '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
      '#FECA57', '#DDA0DD', '#98D8C8', '#F7DC6F',
    ];
    return colors[DateTime.now().millisecond % colors.length];
  }

  String _formatDuration(Duration duration) {
    if (duration.inHours > 0) {
      return '${duration.inHours}h ${duration.inMinutes % 60}m';
    } else if (duration.inMinutes > 0) {
      return '${duration.inMinutes}m';
    } else {
      return '${duration.inSeconds}s';
    }
  }
}