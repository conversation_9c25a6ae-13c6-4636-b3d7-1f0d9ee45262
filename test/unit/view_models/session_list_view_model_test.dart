import 'package:brainstorming/domain/entities/session.dart';
import 'package:brainstorming/domain/failures/failures.dart';
import 'package:brainstorming/domain/repositories/session_repository.dart';
import 'package:brainstorming/presentation/view_models/session_list_view_model.dart';
import 'package:dartz/dartz.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';

@GenerateMocks([SessionRepository])
import 'session_list_view_model_test.mocks.dart';

void main() {
  late SessionListViewModel viewModel;
  late MockSessionRepository mockSessionRepository;

  setUp(() {
    mockSessionRepository = MockSessionRepository();

    // Set up default mock stubs
    when(mockSessionRepository.getRecentSessions(limit: anyNamed('limit')))
        .thenAnswer((_) async => const Right([]));
    when(mockSessionRepository.getArchivedSessions())
        .thenAnswer((_) async => const Right([]));
  });

  SessionListViewModel createViewModel() {
    return SessionListViewModel(mockSessionRepository);
  }

  group('SessionListViewModel', () {
    final testRecentSessions = [
      Session(
        id: '1',
        title: 'Recent Session 1',
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now(),
        tags: const ['tag1'],
      ),
      Session(
        id: '2',
        title: 'Recent Session 2',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 1)),
        tags: const ['tag2'],
      ),
    ];

    final testArchivedSessions = [
      Session(
        id: '3',
        title: 'Archived Session 1',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
        tags: const ['old'],
        isArchived: true,
      ),
    ];

    test('should load sessions on initialization', () async {
      // Arrange
      when(mockSessionRepository.getRecentSessions())
          .thenAnswer((_) async => Right(testRecentSessions));
      when(mockSessionRepository.getArchivedSessions())
          .thenAnswer((_) async => Right(testArchivedSessions));

      // Act
      viewModel = createViewModel();
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.recentSessions, testRecentSessions);
      expect(viewModel.state.archivedSessions, testArchivedSessions);
      expect(viewModel.state.error, isNull);
    });

    test('should handle error when loading recent sessions fails', () async {
      // Arrange
      final failure = StorageFailure.databaseError('Database error');
      when(mockSessionRepository.getRecentSessions())
          .thenAnswer((_) async => Left(failure));
      when(mockSessionRepository.getArchivedSessions())
          .thenAnswer((_) async => Right(testArchivedSessions));

      // Act
      viewModel = createViewModel();
      await Future.delayed(const Duration(milliseconds: 100));

      // Assert
      expect(viewModel.state.isLoading, false);
      expect(viewModel.state.error, failure.message);
    });

    test('should search sessions with query', () async {
      // Arrange
      const searchQuery = 'test';
      final searchResults = [testRecentSessions.first];
      
      when(mockSessionRepository.searchSessions(searchQuery))
          .thenAnswer((_) async => Right(searchResults));

      viewModel = createViewModel();

      // Act
      await viewModel.searchSessions(searchQuery);

      // Assert
      expect(viewModel.state.searchQuery, searchQuery);
      expect(viewModel.state.recentSessions, searchResults);
      expect(viewModel.state.archivedSessions, isEmpty);
      verify(mockSessionRepository.searchSessions(searchQuery));
    });

    test('should reload all sessions when search query is empty', () async {
      // Arrange
      when(mockSessionRepository.getRecentSessions())
          .thenAnswer((_) async => Right(testRecentSessions));
      when(mockSessionRepository.getArchivedSessions())
          .thenAnswer((_) async => Right(testArchivedSessions));

      viewModel = createViewModel();

      // Act
      await viewModel.searchSessions('');

      // Assert
      expect(viewModel.state.searchQuery, '');
      verify(mockSessionRepository.getRecentSessions()).called(2); // Once on init, once on search
      verify(mockSessionRepository.getArchivedSessions()).called(2);
    });

    test('should delete session and reload', () async {
      // Arrange
      const sessionId = '1';
      
      when(mockSessionRepository.deleteSession(sessionId))
          .thenAnswer((_) async => const Right(null));
      when(mockSessionRepository.getRecentSessions())
          .thenAnswer((_) async => Right(testRecentSessions));
      when(mockSessionRepository.getArchivedSessions())
          .thenAnswer((_) async => Right(testArchivedSessions));

      viewModel = createViewModel();

      // Act
      await viewModel.deleteSession(sessionId);

      // Assert
      verify(mockSessionRepository.deleteSession(sessionId));
      verify(mockSessionRepository.getRecentSessions()).called(2);
    });

    test('should archive session and reload', () async {
      // Arrange
      const sessionId = '1';
      
      when(mockSessionRepository.archiveSession(sessionId))
          .thenAnswer((_) async => const Right(null));
      when(mockSessionRepository.getRecentSessions())
          .thenAnswer((_) async => Right(testRecentSessions));
      when(mockSessionRepository.getArchivedSessions())
          .thenAnswer((_) async => Right(testArchivedSessions));

      viewModel = createViewModel();

      // Act
      await viewModel.archiveSession(sessionId);

      // Assert
      verify(mockSessionRepository.archiveSession(sessionId));
      verify(mockSessionRepository.getRecentSessions()).called(2);
    });

    test('should unarchive session and reload', () async {
      // Arrange
      const sessionId = '3';
      
      when(mockSessionRepository.unarchiveSession(sessionId))
          .thenAnswer((_) async => const Right(null));
      when(mockSessionRepository.getRecentSessions())
          .thenAnswer((_) async => Right(testRecentSessions));
      when(mockSessionRepository.getArchivedSessions())
          .thenAnswer((_) async => Right(testArchivedSessions));

      viewModel = createViewModel();

      // Act
      await viewModel.unarchiveSession(sessionId);

      // Assert
      verify(mockSessionRepository.unarchiveSession(sessionId));
      verify(mockSessionRepository.getRecentSessions()).called(2);
    });

    test('should handle deletion failure', () async {
      // Arrange
      const sessionId = '1';
      final failure = StorageFailure.databaseError('Delete failed');
      
      when(mockSessionRepository.deleteSession(sessionId))
          .thenAnswer((_) async => Left(failure));

      viewModel = createViewModel();

      // Clear mock interactions after initialization to focus on deleteSession behavior
      clearInteractions(mockSessionRepository);
      when(mockSessionRepository.deleteSession(sessionId))
          .thenAnswer((_) async => Left(failure));

      // Act
      await viewModel.deleteSession(sessionId);

      // Assert
      expect(viewModel.state.error, failure.message);
      verify(mockSessionRepository.deleteSession(sessionId));
      verifyNever(mockSessionRepository.getRecentSessions());
    });
  });
}