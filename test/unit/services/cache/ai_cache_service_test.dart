import 'package:brainstorming/data/services/cache/ai_cache_service.dart';
import 'package:brainstorming/domain/entities/ai/ai_entities.dart';
import 'package:brainstorming/domain/entities/ai/cross_session_insights.dart';
import 'package:brainstorming/domain/entities/ai/mood_indicator.dart';
import 'package:brainstorming/domain/entities/ai/question.dart';
import 'package:brainstorming/domain/entities/ai/session_insights.dart';
import 'package:brainstorming/domain/entities/ai/user_profile.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

void main() {
  late AICacheService cacheService;

  setUp(() async {
    SharedPreferences.setMockInitialValues({});
    cacheService = AICacheService();
    await cacheService.initialize();
  });

  tearDown(() {
    cacheService.dispose();
  });

  group('AICacheService', () {
    group('Session Insights Caching', () {
      test('should cache and retrieve session insights', () async {
        // Arrange
        const sessionId = 'session123';
        final testTime = DateTime.now();
        final insights = SessionInsights(
          sessionId: sessionId,
          keyPoints: [
            KeyPoint(
              id: 'kp1',
              content: 'Point 1',
              importance: 0.8,
              timestamp: testTime,
            ),
            KeyPoint(
              id: 'kp2',
              content: 'Point 2',
              importance: 0.7,
              timestamp: testTime,
            ),
          ],
          topics: const ['Topic 1', 'Topic 2'],
          patterns: const [],
          engagementScore: 0.75,
          generatedAt: testTime,
          summary: 'Test summary',
        );

        // Act
        await cacheService.cacheSessionInsights(sessionId, insights);
        final retrieved = await cacheService.getCachedSessionInsights(sessionId);

        // Assert
        expect(retrieved, isNotNull);
        expect(retrieved!.sessionId, sessionId);
        expect(retrieved.keyPoints, equals(insights.keyPoints));
        expect(retrieved.summary, equals(insights.summary));
      });

      test('should return null for non-existent session insights', () async {
        // Act
        final result = await cacheService.getCachedSessionInsights('nonexistent');

        // Assert
        expect(result, isNull);
      });

      test('should invalidate session cache', () async {
        // Arrange
        const sessionId = 'session123';
        final insights = SessionInsights(
          sessionId: sessionId,
          keyPoints: [
            KeyPoint(
              id: 'kp1',
              content: 'Point 1',
              importance: 0.8,
              timestamp: DateTime.now(),
            ),
          ],
          topics: const ['Topic 1'],
          patterns: const [],
          engagementScore: 0.75,
          generatedAt: DateTime.now(),
          summary: 'Summary',
        );

        await cacheService.cacheSessionInsights(sessionId, insights);

        // Act
        await cacheService.invalidateSessionCaches(sessionId);
        final retrieved = await cacheService.getCachedSessionInsights(sessionId);

        // Assert
        expect(retrieved, isNull);
      });
    });

    group('User Profile Caching', () {
      test('should cache and retrieve user profile', () async {
        // Arrange
        const userId = 'user123';
        final profile = UserProfile(
          userId: userId,
          questionPrefs: const QuestionPreferences(
            preferredType: QuestionType.analytical,
            preferredComplexity: QuestionComplexity.complex,
            openEndedPreference: 0.8,
          ),
          patterns: const [],
          topicInterests: const {'AI': 0.8, 'ML': 0.7},
          lastUpdated: DateTime.now(),
        );

        // Act
        await cacheService.cacheUserProfile(userId, profile);
        final retrieved = await cacheService.getCachedUserProfile(userId);

        // Assert
        expect(retrieved, isNotNull);
        expect(retrieved!.userId, userId);
        expect(retrieved.questionPrefs.preferredComplexity, QuestionComplexity.complex);
        expect(retrieved.topicInterests['AI'], 0.8);
      });
    });

    group('Mood Analysis Caching', () {
      test('should cache and retrieve mood analysis', () async {
        // Arrange
        const sessionId = 'session123';
        final moods = [
          MoodIndicator(
            level: MoodLevel.high,
            score: 0.8,
            confidence: 0.9,
            timestamp: DateTime.now(),
          ),
          MoodIndicator(
            level: MoodLevel.medium,
            score: 0.5,
            confidence: 0.7,
            timestamp: DateTime.now(),
          ),
        ];

        // Act
        await cacheService.cacheMoodAnalysis(sessionId, moods);
        final retrieved = await cacheService.getCachedMoodAnalysis(sessionId);

        // Assert
        expect(retrieved, isNotNull);
        expect(retrieved!.length, 2);
        expect(retrieved[0].level, MoodLevel.high);
        expect(retrieved[0].score, 0.8);
        expect(retrieved[1].score, 0.5);
      });
    });

    group('Adaptive Questions Caching', () {
      test('should cache adaptive questions with memory cache', () async {
        // Arrange
        const contextKey = 'context123';
        final questions = [
          const AdaptiveQuestion(
            question: 'What specific aspect interests you?',
            type: 'exploratory',
            complexity: 'balanced',
            followUpTopics: ['topic1', 'topic2'],
          ),
          const AdaptiveQuestion(
            question: 'Can you elaborate on that?',
            type: 'clarifying',
            complexity: 'simple',
            followUpTopics: [],
          ),
        ];

        // Act
        await cacheService.cacheAdaptiveQuestions(contextKey, questions);
        final retrieved = await cacheService.getCachedAdaptiveQuestions(contextKey);

        // Assert
        expect(retrieved, isNotNull);
        expect(retrieved!.length, 2);
        expect(retrieved[0].question, contains('specific aspect'));
        expect(retrieved[1].type, 'clarifying');
      });
    });

    group('Cross-Session Insights Caching', () {
      test('should cache and retrieve cross-session insights', () async {
        // Arrange
        const userId = 'user123';
        final insights = CrossSessionInsights(
          recurringThemes: const [
            Pattern(
              id: 'pattern1',
              theme: 'AI Development',
              description: 'Frequent discussions about AI',
              frequency: 5,
              sessionIds: ['session1', 'session2'],
              confidence: 0.85,
            ),
          ],
          ideaEvolutions: const [
            Evolution(
              id: 'evolution1',
              originalIdea: 'Basic chatbot',
              stages: [],
              currentState: 'Advanced AI assistant',
            ),
          ],
          topicConnections: const {
            'AI': ['Machine Learning', 'Neural Networks'],
            'Web Dev': ['React', 'TypeScript'],
          },
          trends: const [],
          confidence: 0.8,
        );

        // Act
        await cacheService.cacheCrossSessionInsights(userId, insights);
        final retrieved = await cacheService.getCachedCrossSessionInsights(userId);

        // Assert
        expect(retrieved, isNotNull);
        expect(retrieved!.recurringThemes.length, 1);
        expect(retrieved.recurringThemes[0].theme, 'AI Development');
        expect(retrieved.ideaEvolutions.length, 1);
        expect(retrieved.topicConnections['AI']!.length, 2);
      });
    });

    group('Cache Management', () {
      test('should invalidate all user caches', () async {
        // Arrange
        const userId = 'user123';
        
        // Cache various data
        await cacheService.cacheUserProfile(
          userId,
          UserProfile(
            userId: userId,
            questionPrefs: const QuestionPreferences(
              preferredType: QuestionType.openEnded,
              preferredComplexity: QuestionComplexity.simple,
              openEndedPreference: 0.3,
            ),
            patterns: const [],
            topicInterests: const {},
            lastUpdated: DateTime.now(),
          ),
        );

        await cacheService.cachePatternAnalysis(userId, [
          const ThinkingPattern(
            name: 'analytical',
            description: 'Analytical thinking pattern',
            confidence: 0.8,
            characteristics: {'strength': 0.8},
          ),
        ]);

        // Act
        await cacheService.invalidateUserCaches(userId);
        
        // Assert
        final profile = await cacheService.getCachedUserProfile(userId);
        final patterns = await cacheService.getCachedPatternAnalysis(userId);
        
        expect(profile, isNull);
        expect(patterns, isNull);
      });

      test('should clear all caches', () async {
        // Arrange
        await cacheService.cacheSessionInsights(
          'session1',
          SessionInsights(
            sessionId: 'session1',
            keyPoints: [
              KeyPoint(
                id: 'kp1',
                content: 'Point',
                importance: 0.8,
                timestamp: DateTime.now(),
              ),
            ],
            topics: const ['Topic'],
            patterns: const [],
            engagementScore: 0.75,
            generatedAt: DateTime.now(),
            summary: 'Summary',
          ),
        );

        await cacheService.cacheUserProfile(
          'user1',
          UserProfile(
            userId: 'user1',
            questionPrefs: const QuestionPreferences(
              preferredType: QuestionType.openEnded,
              preferredComplexity: QuestionComplexity.simple,
              openEndedPreference: 0.3,
            ),
            patterns: const [],
            topicInterests: const {},
            lastUpdated: DateTime.now(),
          ),
        );

        // Act
        await cacheService.clearAllCaches();

        // Assert
        final sessionInsights = await cacheService.getCachedSessionInsights('session1');
        final userProfile = await cacheService.getCachedUserProfile('user1');
        
        expect(sessionInsights, isNull);
        expect(userProfile, isNull);
      });

      test('should provide cache statistics', () async {
        // Arrange
        await cacheService.cacheAdaptiveQuestions('context1', [
          const AdaptiveQuestion(
            question: 'Question 1',
            type: 'exploratory',
            complexity: 'simple',
            followUpTopics: [],
          ),
        ]);

        // Act
        final stats = cacheService.getStatistics();

        // Assert
        expect(stats.memoryCacheCount, greaterThan(0));
        expect(stats.memoryCacheSizeBytes, greaterThan(0));
        expect(stats.memoryCacheSizeFormatted, isNotEmpty);
      });
    });
  });
}