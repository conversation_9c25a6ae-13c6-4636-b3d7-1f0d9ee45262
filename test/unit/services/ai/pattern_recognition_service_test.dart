import 'package:brainstorming/data/services/ai/pattern_recognition_service.dart';
import 'package:brainstorming/domain/entities/message.dart';
import 'package:brainstorming/domain/entities/session.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late PatternRecognitionService patternRecognitionService;

  setUp(() {
    patternRecognitionService = PatternRecognitionService();
  });

  group('PatternRecognitionService', () {
    group('identifyPatterns', () {
      test('should identify analytical thinking pattern', () async {
        // Arrange
        final sessions = [
          Session(
            id: 'session1',
            title: 'Problem Analysis',
            messages: [
              Message(
                id: '1',
                sessionId: 'session1',
                content: 'Let me break down this problem into smaller parts',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '2',
                sessionId: 'session1',
                content: 'First, we need to analyze the root cause',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '3',
                sessionId: 'session1',
                content: 'Based on the data, I can see three main issues',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
        ];

        // Act
        final patterns = await patternRecognitionService.identifyPatterns(
          sessions: sessions,
          userId: 'user123',
        );

        // Assert
        expect(patterns.isNotEmpty, true);
        expect(
          patterns.any((p) => p.type == 'analytical' || p.type == 'systematic_solver'),
          true,
        );
      });

      test('should identify creative thinking pattern', () async {
        // Arrange
        final sessions = [
          Session(
            id: 'session1',
            title: 'Brainstorming',
            messages: [
              Message(
                id: '1',
                sessionId: 'session1',
                content: 'What if we tried something completely different?',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '2',
                sessionId: 'session1',
                content: 'I have a wild idea - let\'s combine these concepts',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '3',
                sessionId: 'session1',
                content: 'Imagine if we could create something unique',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
        ];

        // Act
        final patterns = await patternRecognitionService.identifyPatterns(
          sessions: sessions,
          userId: 'user123',
        );

        // Assert
        expect(patterns.isNotEmpty, true);
        expect(
          patterns.any((p) => p.type == 'creative' || p.type == 'creative_solver'),
          true,
        );
      });

      test('should handle sessions with mixed patterns', () async {
        // Arrange
        final sessions = [
          Session(
            id: 'session1',
            title: 'Mixed Thinking',
            messages: [
              Message(
                id: '1',
                sessionId: 'session1',
                content: 'Let\'s analyze this systematically',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '2',
                sessionId: 'session1',
                content: 'But also think outside the box',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '3',
                sessionId: 'session1',
                content: 'What if we combined analytical and creative approaches?',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
        ];

        // Act
        final patterns = await patternRecognitionService.identifyPatterns(
          sessions: sessions,
          userId: 'user123',
        );

        // Assert
        expect(patterns.length, greaterThanOrEqualTo(2));
        // Should identify both analytical and creative patterns
      });
    });

    group('findRecurringThemes', () {
      test('should identify recurring themes across sessions', () async {
        // Arrange
        final sessions = [
          Session(
            id: 'session1',
            title: 'Web Development',
            messages: [
              Message(
                id: '1',
                sessionId: 'session1',
                content: 'I want to build a React application',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '2',
                sessionId: 'session1',
                content: 'What about using TypeScript?',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now().subtract(const Duration(days: 2)),
            updatedAt: DateTime.now().subtract(const Duration(days: 2)),
            userId: 'user123',
          ),
          Session(
            id: 'session2',
            title: 'Frontend Architecture',
            messages: [
              Message(
                id: '3',
                sessionId: 'session2',
                content: 'React state management is important',
                isAi: false,
                timestamp: DateTime.now(),
              ),
              Message(
                id: '4',
                sessionId: 'session2',
                content: 'TypeScript helps with type safety',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
        ];

        // Act
        final themes = await patternRecognitionService.findRecurringThemes(sessions);

        // Assert
        expect(themes.isNotEmpty, true);
        expect(
          themes.any((t) => 
            t.theme.toLowerCase().contains('react') || 
            t.theme.toLowerCase().contains('typescript') ||
            t.theme.toLowerCase().contains('frontend')
          ),
          true,
        );
        
        // Check that recurring themes have frequency > 1
        final recurringThemes = themes.where((t) => t.frequency > 1);
        expect(recurringThemes.isNotEmpty, true);
      });

      test('should handle sessions with no recurring themes', () async {
        // Arrange
        final sessions = [
          Session(
            id: 'session1',
            title: 'Cooking Adventures',
            messages: [
              Message(
                id: '1',
                sessionId: 'session1',
                content: 'Discussing delicious recipes',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
          Session(
            id: 'session2',
            title: 'Vacation Planning',
            messages: [
              Message(
                id: '2',
                sessionId: 'session2',
                content: 'Organizing travel itinerary',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
        ];

        // Act
        final themes = await patternRecognitionService.findRecurringThemes(sessions);

        // Assert
        // Should handle sessions with different content gracefully
        expect(themes, isA<List<RecurringTheme>>());
      });
    });

    group('detectBehavioralTrends', () {
      test('should detect increasing engagement trend', () async {
        // Arrange
        final sessions = [
          Session(
            id: 'session1',
            title: 'Session 1',
            messages: List.generate(3, (i) => Message(
              id: 'msg$i',
              sessionId: 'session1',
              content: 'Short message',
              isAi: false,
              timestamp: DateTime.now(),
            )),
            createdAt: DateTime.now().subtract(const Duration(days: 7)),
            updatedAt: DateTime.now().subtract(const Duration(days: 7)),
            userId: 'user123',
          ),
          Session(
            id: 'session2',
            title: 'Session 2',
            messages: List.generate(5, (i) => Message(
              id: 'msg$i',
              sessionId: 'session2',
              content: 'Medium length message with more content',
              isAi: false,
              timestamp: DateTime.now(),
            )),
            createdAt: DateTime.now().subtract(const Duration(days: 3)),
            updatedAt: DateTime.now().subtract(const Duration(days: 3)),
            userId: 'user123',
          ),
          Session(
            id: 'session3',
            title: 'Session 3',
            messages: List.generate(8, (i) => Message(
              id: 'msg$i',
              sessionId: 'session3',
              content: 'Longer and more detailed message showing higher engagement',
              isAi: false,
              timestamp: DateTime.now(),
            )),
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
        ];

        // Act
        final trends = await patternRecognitionService.detectBehavioralTrends(
          sessions,
          'user123',
        );

        // Assert
        expect(trends.isNotEmpty, true);
        expect(
          trends.any((t) => 
            t.type == 'engagement' && 
            (t.direction == 'increasing' || t.trend == 'increasing')
          ),
          true,
        );
      });

      test('should detect time preference trends', () async {
        // Arrange
        final morningTime = DateTime(2024, 1, 1, 9, 0); // 9 AM
        final sessions = List.generate(5, (i) => Session(
          id: 'session$i',
          title: 'Morning Session $i',
          messages: [
            Message(
              id: 'msg$i',
              sessionId: 'session$i',
              content: 'Morning brainstorming',
              isAi: false,
              timestamp: morningTime.add(Duration(days: i)),
            ),
          ],
          createdAt: morningTime.add(Duration(days: i)),
          updatedAt: morningTime.add(Duration(days: i)),
          userId: 'user123',
        ));

        // Act
        final trends = await patternRecognitionService.detectBehavioralTrends(
          sessions,
          'user123',
        );

        // Assert
        expect(trends.isNotEmpty, true);
        expect(
          trends.any((t) => 
            t.type == 'time_preference' || 
            t.description.toLowerCase().contains('morning')
          ),
          true,
        );
      });

      test('should handle insufficient data for trends', () async {
        // Arrange
        final sessions = [
          Session(
            id: 'session1',
            title: 'Single Session',
            messages: [
              Message(
                id: '1',
                sessionId: 'session1',
                content: 'Just one session',
                isAi: false,
                timestamp: DateTime.now(),
              ),
            ],
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            userId: 'user123',
          ),
        ];

        // Act
        final trends = await patternRecognitionService.detectBehavioralTrends(
          sessions,
          'user123',
        );

        // Assert
        // Should return minimal or no trends with single session
        expect(trends.length, lessThanOrEqualTo(1));
      });
    });
  });
}