import 'package:brainstorming/domain/entities/ai/ai_entities.dart';
import 'package:brainstorming/presentation/widgets/ai/adaptive_questions_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('AdaptiveQuestionsWidget', () {
    testWidgets('displays all questions', (WidgetTester tester) async {
      // Arrange
      final questions = [
        const AdaptiveQuestion(
          question: 'What specific aspect interests you most?',
          type: 'exploratory',
          complexity: 'balanced',
          followUpTopics: ['topic1', 'topic2'],
        ),
        const AdaptiveQuestion(
          question: 'Can you elaborate on your goals?',
          type: 'clarifying',
          complexity: 'simple',
          followUpTopics: [],
        ),
        const AdaptiveQuestion(
          question: 'How do you envision the implementation?',
          type: 'analytical',
          complexity: 'detailed',
          followUpTopics: ['implementation', 'architecture'],
        ),
      ];

      AdaptiveQuestion? selectedQuestion;

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdaptiveQuestionsWidget(
              questions: questions,
              onQuestionSelected: (question) {
                selectedQuestion = question;
              },
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Suggested questions'), findsOneWidget);
      expect(find.text('What specific aspect interests you most?'), findsOneWidget);
      expect(find.text('Can you elaborate on your goals?'), findsOneWidget);
      expect(find.text('How do you envision the implementation?'), findsOneWidget);
    });

    testWidgets('calls callback when question is tapped', (WidgetTester tester) async {
      // Arrange
      final questions = [
        const AdaptiveQuestion(
          question: 'Test question 1',
          type: 'exploratory',
          complexity: 'simple',
          followUpTopics: [],
        ),
        const AdaptiveQuestion(
          question: 'Test question 2',
          type: 'analytical',
          complexity: 'balanced',
          followUpTopics: [],
        ),
      ];

      AdaptiveQuestion? selectedQuestion;

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdaptiveQuestionsWidget(
              questions: questions,
              onQuestionSelected: (question) {
                selectedQuestion = question;
              },
            ),
          ),
        ),
      );

      // Act
      await tester.tap(find.text('Test question 1'));
      await tester.pumpAndSettle();

      // Assert
      expect(selectedQuestion, 'Test question 1');

      // Act - tap second question
      await tester.tap(find.text('Test question 2'));
      await tester.pumpAndSettle();

      // Assert
      expect(selectedQuestion, 'Test question 2');
    });

    testWidgets('shows question type icons correctly', (WidgetTester tester) async {
      // Arrange
      final questions = [
        const AdaptiveQuestion(
          question: 'Exploratory question',
          type: 'exploratory',
          complexity: 'simple',
          followUpTopics: [],
        ),
        const AdaptiveQuestion(
          question: 'Analytical question',
          type: 'analytical',
          complexity: 'detailed',
          followUpTopics: [],
        ),
        const AdaptiveQuestion(
          question: 'Creative question',
          type: 'creative',
          complexity: 'balanced',
          followUpTopics: [],
        ),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdaptiveQuestionsWidget(
              questions: questions,
              onQuestionSelected: (_) {},
            ),
          ),
        ),
      );

      // Assert - check for different icons
      expect(find.byIcon(Icons.explore), findsOneWidget); // exploratory
      expect(find.byIcon(Icons.analytics), findsOneWidget); // analytical
      expect(find.byIcon(Icons.lightbulb), findsOneWidget); // creative
    });

    testWidgets('scrolls horizontally when many questions', (WidgetTester tester) async {
      // Arrange
      final questions = List.generate(
        10,
        (i) => AdaptiveQuestion(
          question: 'Question $i with a longer text to ensure scrolling',
          type: 'exploratory',
          complexity: 'simple',
          followUpTopics: const [],
        ),
      );

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdaptiveQuestionsWidget(
              questions: questions,
              onQuestionSelected: (_) {},
            ),
          ),
        ),
      );

      // Assert - check that not all questions are visible initially
      expect(find.text('Question 0 with a longer text to ensure scrolling'), findsOneWidget);
      expect(find.text('Question 9 with a longer text to ensure scrolling'), findsNothing);

      // Act - scroll to see more questions
      await tester.dragFrom(
        tester.getCenter(find.byType(SingleChildScrollView)),
        const Offset(-300, 0),
      );
      await tester.pumpAndSettle();

      // Assert - later questions should now be visible
      expect(find.text('Question 9 with a longer text to ensure scrolling'), findsOneWidget);
    });

    testWidgets('handles empty questions list', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdaptiveQuestionsWidget(
              questions: const [],
              onQuestionSelected: (_) {},
            ),
          ),
        ),
      );

      // Assert - widget should handle empty list gracefully
      expect(find.text('Suggested questions'), findsOneWidget);
      expect(find.byType(InkWell), findsNothing);
    });

    testWidgets('shows follow-up topics when available', (WidgetTester tester) async {
      // Arrange
      final questions = [
        const AdaptiveQuestion(
          question: 'Question with topics',
          type: 'exploratory',
          complexity: 'balanced',
          followUpTopics: ['Architecture', 'Design Patterns', 'Best Practices'],
        ),
      ];

      // Act
      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdaptiveQuestionsWidget(
              questions: questions,
              onQuestionSelected: (_) {},
            ),
          ),
        ),
      );

      // Assert - check for follow-up topic chips
      expect(find.text('Architecture'), findsOneWidget);
      expect(find.text('Design Patterns'), findsOneWidget);
      expect(find.text('Best Practices'), findsOneWidget);
    });
  });
}