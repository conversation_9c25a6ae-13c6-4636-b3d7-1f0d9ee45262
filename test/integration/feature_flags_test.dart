import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../helpers/test_setup.dart';
import 'package:brainstorming/core/features/feature_flags.dart';

void main() {
  group('Feature Flags Integration Tests', () {
    setUp(() {
      TestSetup.setUp();
    });

    test('FeatureFlagService should work with mocked SharedPreferences', () async {
      // Setup mock SharedPreferences with clean state
      SharedPreferences.setMockInitialValues({});

      final prefs = await SharedPreferences.getInstance();
      final service = FeatureFlagService(prefs);

      // Test default values first
      expect(service.isEnabled(FeatureFlag.adaptiveQuestioning), isFalse); // Default is false
      expect(service.isEnabled(FeatureFlag.contextBridging), isTrue); // Default is true

      // Test setting feature flags
      await service.setEnabled(FeatureFlag.adaptiveQuestioning, true);
      expect(service.isEnabled(FeatureFlag.adaptiveQuestioning), isTrue);

      // Test resetting to default
      await service.resetToDefault(FeatureFlag.adaptiveQuestioning);
      expect(service.isEnabled(FeatureFlag.adaptiveQuestioning), isFalse);
    });

    test('Feature flags should have correct default values', () async {
      SharedPreferences.setMockInitialValues({});
      final prefs = await SharedPreferences.getInstance();
      final service = FeatureFlagService(prefs);

      // Test core features that should be enabled by default
      expect(service.isEnabled(FeatureFlag.contextBridging), isTrue);
      expect(service.isEnabled(FeatureFlag.sessionSummaries), isTrue);
      expect(service.isEnabled(FeatureFlag.ideaValidation), isTrue);
      expect(service.isEnabled(FeatureFlag.ideaPrioritization), isTrue);

      // Test advanced features that should be disabled by default
      expect(service.isEnabled(FeatureFlag.adaptiveQuestioning), isFalse);
      expect(service.isEnabled(FeatureFlag.userModeling), isFalse);
      expect(service.isEnabled(FeatureFlag.vectorEmbeddings), isFalse);
    });

    test('Feature flag helper methods should work correctly', () async {
      SharedPreferences.setMockInitialValues({
        'feature_context_bridging': true,
        'feature_session_summaries': true,
        'feature_idea_validation': true,
        'feature_idea_prioritization': true,
      });

      final prefs = await SharedPreferences.getInstance();
      final service = FeatureFlagService(prefs);

      // Test core AI features check
      expect(service.hasCoreAIFeaturesEnabled(), isTrue);

      // Test any AI features check
      expect(service.hasAnyAIFeaturesEnabled(), isTrue);
    });
  });
}
