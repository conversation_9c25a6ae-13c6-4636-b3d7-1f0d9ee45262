import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:go_router/go_router.dart';

import '../helpers/test_setup.dart';
import 'package:brainstorming/presentation/app.dart';

void main() {
  group('Navigation Tests', () {
    setUp(() {
      TestSetup.setUp();
    });

    testWidgets('App should start and navigate to home', (tester) async {
      await tester.pumpWithProviders(
        const BrainstormingApp(),
      );

      // Wait for any async operations
      await tester.pumpAndSettle();

      // Verify the app loads without crashing
      expect(find.byType(BrainstormingApp), findsOneWidget);
    });

    testWidgets('Router should handle all defined routes', (tester) async {
      final router = GoRouter(
        initialLocation: '/',
        routes: [
          GoRoute(
            path: '/',
            builder: (context, state) => const Scaffold(
              body: Text('Home'),
            ),
          ),
          GoRoute(
            path: '/analytics/:sessionId',
            builder: (context, state) => Scaffold(
              body: Text('Analytics: ${state.pathParameters['sessionId']}'),
            ),
          ),
          GoRoute(
            path: '/insights/:userId',
            builder: (context, state) => Scaffold(
              body: Text('Insights: ${state.pathParameters['userId']}'),
            ),
          ),
          GoRoute(
            path: '/profile',
            builder: (context, state) => const Scaffold(
              body: Text('Profile'),
            ),
          ),
          GoRoute(
            path: '/settings',
            builder: (context, state) => const Scaffold(
              body: Text('Settings'),
            ),
          ),
        ],
      );

      await tester.pumpWidget(
        TestSetup.createProviderScope(
          child: MaterialApp.router(
            routerConfig: router,
          ),
        ),
      );

      await tester.pumpAndSettle();

      // Test navigation to different routes
      router.go('/analytics/test-session');
      await tester.pumpAndSettle();
      expect(find.text('Analytics: test-session'), findsOneWidget);

      router.go('/insights/test-user');
      await tester.pumpAndSettle();
      expect(find.text('Insights: test-user'), findsOneWidget);

      router.go('/profile');
      await tester.pumpAndSettle();
      expect(find.text('Profile'), findsOneWidget);

      router.go('/settings');
      await tester.pumpAndSettle();
      expect(find.text('Settings'), findsOneWidget);
    });
  });
}
