import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:brainstorming/core/features/feature_flags.dart';

/// Test setup helper for configuring providers and mocks
class TestSetup {
  /// Sets up SharedPreferences mock with default values
  static void setupSharedPreferences([Map<String, Object>? initialValues]) {
    SharedPreferences.setMockInitialValues(initialValues ?? {});
  }

  /// Creates a ProviderScope with properly mocked SharedPreferences
  static Widget createProviderScope({
    required Widget child,
    Map<String, Object>? sharedPrefsValues,
    List<Override>? additionalOverrides,
  }) {
    setupSharedPreferences(sharedPrefsValues);

    return FutureBuilder<SharedPreferences>(
      future: SharedPreferences.getInstance(),
      builder: (context, snapshot) {
        if (!snapshot.hasData) {
          return const CircularProgressIndicator();
        }

        return ProviderScope(
          overrides: [
            sharedPreferencesProvider.overrideWithValue(snapshot.data!),
            ...?additionalOverrides,
          ],
          child: child,
        );
      },
    );
  }

  /// Sets up common test environment
  static void setUp() {
    TestWidgetsFlutterBinding.ensureInitialized();
    setupSharedPreferences();
  }

  /// Cleans up after tests
  static void tearDown() {
    // Reset any global state if needed
  }
}

/// Extension to make test setup easier
extension WidgetTesterTestSetup on WidgetTester {
  /// Pumps a widget with proper provider setup
  Future<void> pumpWithProviders(
    Widget widget, {
    Map<String, Object>? sharedPrefsValues,
    List<Override>? overrides,
  }) async {
    await pumpWidget(
      TestSetup.createProviderScope(
        child: widget,
        sharedPrefsValues: sharedPrefsValues,
        additionalOverrides: overrides,
      ),
    );
  }
}
